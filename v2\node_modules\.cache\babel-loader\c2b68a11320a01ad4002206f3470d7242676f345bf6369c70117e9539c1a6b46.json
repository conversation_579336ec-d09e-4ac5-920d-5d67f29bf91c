{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\pages\\\\client\\\\analytics\\\\ProductPerformance.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { <PERSON><PERSON>hart, Bar, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductPerformance = () => {\n  _s();\n  const [timeRange, setTimeRange] = useState('7d');\n  const [selectedMetric, setSelectedMetric] = useState('tryOns');\n\n  // Sample data\n  const productData = [{\n    name: 'Watch Model A',\n    tryOns: 450,\n    views: 1200,\n    conversions: 45,\n    avgDuration: 180\n  }, {\n    name: 'Watch Model B',\n    tryOns: 380,\n    views: 980,\n    conversions: 38,\n    avgDuration: 165\n  }, {\n    name: 'Watch Model C',\n    tryOns: 290,\n    views: 850,\n    conversions: 29,\n    avgDuration: 150\n  }, {\n    name: 'Watch Model D',\n    tryOns: 210,\n    views: 720,\n    conversions: 21,\n    avgDuration: 140\n  }, {\n    name: 'Watch Model E',\n    tryOns: 180,\n    views: 650,\n    conversions: 18,\n    avgDuration: 135\n  }];\n  const trendData = [{\n    date: '2024-03-01',\n    tryOns: 120,\n    views: 320,\n    conversions: 12\n  }, {\n    date: '2024-03-02',\n    tryOns: 150,\n    views: 380,\n    conversions: 15\n  }, {\n    date: '2024-03-03',\n    tryOns: 180,\n    views: 420,\n    conversions: 18\n  }, {\n    date: '2024-03-04',\n    tryOns: 160,\n    views: 400,\n    conversions: 16\n  }, {\n    date: '2024-03-05',\n    tryOns: 200,\n    views: 450,\n    conversions: 20\n  }, {\n    date: '2024-03-06',\n    tryOns: 220,\n    views: 480,\n    conversions: 22\n  }, {\n    date: '2024-03-07',\n    tryOns: 250,\n    views: 520,\n    conversions: 25\n  }];\n  const metrics = [{\n    id: 'tryOns',\n    label: 'Try-Ons'\n  }, {\n    id: 'views',\n    label: 'Views'\n  }, {\n    id: 'conversions',\n    label: 'Conversions'\n  }, {\n    id: 'avgDuration',\n    label: 'Avg. Duration'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 space-y-4 md:space-y-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-flex rounded-lg border border-gray-200 p-1\",\n        children: ['7d', '30d', '90d', '1y'].map(range => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setTimeRange(range),\n          className: `px-3 py-1 text-sm font-medium rounded-md ${timeRange === range ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:text-gray-900'}`,\n          children: range\n        }, range, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-flex rounded-lg border border-gray-200 p-1\",\n        children: metrics.map(metric => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setSelectedMetric(metric.id),\n          className: `px-3 py-1 text-sm font-medium rounded-md ${selectedMetric === metric.id ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:text-gray-900'}`,\n          children: metric.label\n        }, metric.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl shadow-sm p-6 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-4\",\n        children: \"Product Performance\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-96\",\n        children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: \"100%\",\n          children: /*#__PURE__*/_jsxDEV(BarChart, {\n            data: productData,\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              dataKey: \"name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Bar, {\n              dataKey: selectedMetric,\n              fill: \"#2D8C88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl shadow-sm p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-4\",\n        children: \"Trend Analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-96\",\n        children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: \"100%\",\n          children: /*#__PURE__*/_jsxDEV(LineChart, {\n            data: trendData,\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              dataKey: \"date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: selectedMetric,\n              stroke: \"#2D8C88\",\n              strokeWidth: 2,\n              dot: {\n                fill: '#2D8C88'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 bg-white rounded-xl shadow-sm overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4 border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: \"Product Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Try-Ons\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Views\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Conversions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Avg. Duration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: productData.map(product => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: product.tryOns\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: product.views\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: product.conversions\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: [product.avgDuration, \"s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this)]\n            }, product.name, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductPerformance, \"2PKGObMcTPW8a2kpm9OcTySpTdw=\");\n_c = ProductPerformance;\nexport default ProductPerformance;\nvar _c;\n$RefreshReg$(_c, \"ProductPerformance\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON><PERSON>", "Bar", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "jsxDEV", "_jsxDEV", "ProductPerformance", "_s", "timeRange", "setTimeRange", "selectedMetric", "setSelectedMetric", "productData", "name", "tryOns", "views", "conversions", "avgDuration", "trendData", "date", "metrics", "id", "label", "className", "children", "map", "range", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "metric", "width", "height", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "fill", "type", "stroke", "strokeWidth", "dot", "product", "_c", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/pages/client/analytics/ProductPerformance.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  LineChart,\n  Line,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  <PERSON><PERSON><PERSON>,\n  Legend,\n  ResponsiveContainer\n} from 'recharts';\n\nconst ProductPerformance = () => {\n  const [timeRange, setTimeRange] = useState('7d');\n  const [selectedMetric, setSelectedMetric] = useState('tryOns');\n\n  // Sample data\n  const productData = [\n    { name: 'Watch Model A', tryOns: 450, views: 1200, conversions: 45, avgDuration: 180 },\n    { name: 'Watch Model B', tryOns: 380, views: 980, conversions: 38, avgDuration: 165 },\n    { name: 'Watch Model C', tryOns: 290, views: 850, conversions: 29, avgDuration: 150 },\n    { name: 'Watch Model D', tryOns: 210, views: 720, conversions: 21, avgDuration: 140 },\n    { name: 'Watch Model E', tryOns: 180, views: 650, conversions: 18, avgDuration: 135 },\n  ];\n\n  const trendData = [\n    { date: '2024-03-01', tryOns: 120, views: 320, conversions: 12 },\n    { date: '2024-03-02', tryOns: 150, views: 380, conversions: 15 },\n    { date: '2024-03-03', tryOns: 180, views: 420, conversions: 18 },\n    { date: '2024-03-04', tryOns: 160, views: 400, conversions: 16 },\n    { date: '2024-03-05', tryOns: 200, views: 450, conversions: 20 },\n    { date: '2024-03-06', tryOns: 220, views: 480, conversions: 22 },\n    { date: '2024-03-07', tryOns: 250, views: 520, conversions: 25 },\n  ];\n\n  const metrics = [\n    { id: 'tryOns', label: 'Try-Ons' },\n    { id: 'views', label: 'Views' },\n    { id: 'conversions', label: 'Conversions' },\n    { id: 'avgDuration', label: 'Avg. Duration' },\n  ];\n\n  return (\n    <div className=\"p-6\">\n      {/* Time Range and Metric Selector */}\n      <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 space-y-4 md:space-y-0\">\n        <div className=\"inline-flex rounded-lg border border-gray-200 p-1\">\n          {['7d', '30d', '90d', '1y'].map((range) => (\n            <button\n              key={range}\n              onClick={() => setTimeRange(range)}\n              className={`px-3 py-1 text-sm font-medium rounded-md ${\n                timeRange === range\n                  ? 'bg-[#2D8C88] text-white'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              {range}\n            </button>\n          ))}\n        </div>\n        <div className=\"inline-flex rounded-lg border border-gray-200 p-1\">\n          {metrics.map((metric) => (\n            <button\n              key={metric.id}\n              onClick={() => setSelectedMetric(metric.id)}\n              className={`px-3 py-1 text-sm font-medium rounded-md ${\n                selectedMetric === metric.id\n                  ? 'bg-[#2D8C88] text-white'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              {metric.label}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Product Performance Chart */}\n      <div className=\"bg-white rounded-xl shadow-sm p-6 mb-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Product Performance</h3>\n        <div className=\"h-96\">\n          <ResponsiveContainer width=\"100%\" height=\"100%\">\n            <BarChart data={productData}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis dataKey=\"name\" />\n              <YAxis />\n              <Tooltip />\n              <Bar dataKey={selectedMetric} fill=\"#2D8C88\" />\n            </BarChart>\n          </ResponsiveContainer>\n        </div>\n      </div>\n\n      {/* Trend Analysis */}\n      <div className=\"bg-white rounded-xl shadow-sm p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Trend Analysis</h3>\n        <div className=\"h-96\">\n          <ResponsiveContainer width=\"100%\" height=\"100%\">\n            <LineChart data={trendData}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis dataKey=\"date\" />\n              <YAxis />\n              <Tooltip />\n              <Line\n                type=\"monotone\"\n                dataKey={selectedMetric}\n                stroke=\"#2D8C88\"\n                strokeWidth={2}\n                dot={{ fill: '#2D8C88' }}\n              />\n            </LineChart>\n          </ResponsiveContainer>\n        </div>\n      </div>\n\n      {/* Product Details Table */}\n      <div className=\"mt-6 bg-white rounded-xl shadow-sm overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Product Details</h3>\n        </div>\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Product</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Try-Ons</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Views</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Conversions</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Avg. Duration</th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {productData.map((product) => (\n                <tr key={product.name} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">{product.name}</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{product.tryOns}</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{product.views}</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{product.conversions}</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{product.avgDuration}s</td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductPerformance; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,QAAQ,EACRC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,QACd,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,QAAQ,CAAC;;EAE9D;EACA,MAAMmB,WAAW,GAAG,CAClB;IAAEC,IAAI,EAAE,eAAe;IAAEC,MAAM,EAAE,GAAG;IAAEC,KAAK,EAAE,IAAI;IAAEC,WAAW,EAAE,EAAE;IAAEC,WAAW,EAAE;EAAI,CAAC,EACtF;IAAEJ,IAAI,EAAE,eAAe;IAAEC,MAAM,EAAE,GAAG;IAAEC,KAAK,EAAE,GAAG;IAAEC,WAAW,EAAE,EAAE;IAAEC,WAAW,EAAE;EAAI,CAAC,EACrF;IAAEJ,IAAI,EAAE,eAAe;IAAEC,MAAM,EAAE,GAAG;IAAEC,KAAK,EAAE,GAAG;IAAEC,WAAW,EAAE,EAAE;IAAEC,WAAW,EAAE;EAAI,CAAC,EACrF;IAAEJ,IAAI,EAAE,eAAe;IAAEC,MAAM,EAAE,GAAG;IAAEC,KAAK,EAAE,GAAG;IAAEC,WAAW,EAAE,EAAE;IAAEC,WAAW,EAAE;EAAI,CAAC,EACrF;IAAEJ,IAAI,EAAE,eAAe;IAAEC,MAAM,EAAE,GAAG;IAAEC,KAAK,EAAE,GAAG;IAAEC,WAAW,EAAE,EAAE;IAAEC,WAAW,EAAE;EAAI,CAAC,CACtF;EAED,MAAMC,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,YAAY;IAAEL,MAAM,EAAE,GAAG;IAAEC,KAAK,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,EAChE;IAAEG,IAAI,EAAE,YAAY;IAAEL,MAAM,EAAE,GAAG;IAAEC,KAAK,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,EAChE;IAAEG,IAAI,EAAE,YAAY;IAAEL,MAAM,EAAE,GAAG;IAAEC,KAAK,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,EAChE;IAAEG,IAAI,EAAE,YAAY;IAAEL,MAAM,EAAE,GAAG;IAAEC,KAAK,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,EAChE;IAAEG,IAAI,EAAE,YAAY;IAAEL,MAAM,EAAE,GAAG;IAAEC,KAAK,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,EAChE;IAAEG,IAAI,EAAE,YAAY;IAAEL,MAAM,EAAE,GAAG;IAAEC,KAAK,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,EAChE;IAAEG,IAAI,EAAE,YAAY;IAAEL,MAAM,EAAE,GAAG;IAAEC,KAAK,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAG,CAAC,CACjE;EAED,MAAMI,OAAO,GAAG,CACd;IAAEC,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,EAClC;IAAED,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC/B;IAAED,EAAE,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC3C;IAAED,EAAE,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAgB,CAAC,CAC9C;EAED,oBACEjB,OAAA;IAAKkB,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAElBnB,OAAA;MAAKkB,SAAS,EAAC,mGAAmG;MAAAC,QAAA,gBAChHnB,OAAA;QAAKkB,SAAS,EAAC,mDAAmD;QAAAC,QAAA,EAC/D,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAACC,GAAG,CAAEC,KAAK,iBACpCrB,OAAA;UAEEsB,OAAO,EAAEA,CAAA,KAAMlB,YAAY,CAACiB,KAAK,CAAE;UACnCH,SAAS,EAAE,4CACTf,SAAS,KAAKkB,KAAK,GACf,yBAAyB,GACzB,mCAAmC,EACtC;UAAAF,QAAA,EAEFE;QAAK,GARDA,KAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASJ,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN1B,OAAA;QAAKkB,SAAS,EAAC,mDAAmD;QAAAC,QAAA,EAC/DJ,OAAO,CAACK,GAAG,CAAEO,MAAM,iBAClB3B,OAAA;UAEEsB,OAAO,EAAEA,CAAA,KAAMhB,iBAAiB,CAACqB,MAAM,CAACX,EAAE,CAAE;UAC5CE,SAAS,EAAE,4CACTb,cAAc,KAAKsB,MAAM,CAACX,EAAE,GACxB,yBAAyB,GACzB,mCAAmC,EACtC;UAAAG,QAAA,EAEFQ,MAAM,CAACV;QAAK,GARRU,MAAM,CAACX,EAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASR,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAKkB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDnB,OAAA;QAAIkB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAmB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/E1B,OAAA;QAAKkB,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBnB,OAAA,CAACF,mBAAmB;UAAC8B,KAAK,EAAC,MAAM;UAACC,MAAM,EAAC,MAAM;UAAAV,QAAA,eAC7CnB,OAAA,CAACX,QAAQ;YAACyC,IAAI,EAAEvB,WAAY;YAAAY,QAAA,gBAC1BnB,OAAA,CAACL,aAAa;cAACoC,eAAe,EAAC;YAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvC1B,OAAA,CAACP,KAAK;cAACuC,OAAO,EAAC;YAAM;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxB1B,OAAA,CAACN,KAAK;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACT1B,OAAA,CAACJ,OAAO;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX1B,OAAA,CAACV,GAAG;cAAC0C,OAAO,EAAE3B,cAAe;cAAC4B,IAAI,EAAC;YAAS;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAKkB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDnB,OAAA;QAAIkB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAc;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1E1B,OAAA;QAAKkB,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBnB,OAAA,CAACF,mBAAmB;UAAC8B,KAAK,EAAC,MAAM;UAACC,MAAM,EAAC,MAAM;UAAAV,QAAA,eAC7CnB,OAAA,CAACT,SAAS;YAACuC,IAAI,EAAEjB,SAAU;YAAAM,QAAA,gBACzBnB,OAAA,CAACL,aAAa;cAACoC,eAAe,EAAC;YAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvC1B,OAAA,CAACP,KAAK;cAACuC,OAAO,EAAC;YAAM;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxB1B,OAAA,CAACN,KAAK;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACT1B,OAAA,CAACJ,OAAO;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX1B,OAAA,CAACR,IAAI;cACH0C,IAAI,EAAC,UAAU;cACfF,OAAO,EAAE3B,cAAe;cACxB8B,MAAM,EAAC,SAAS;cAChBC,WAAW,EAAE,CAAE;cACfC,GAAG,EAAE;gBAAEJ,IAAI,EAAE;cAAU;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAKkB,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBACjEnB,OAAA;QAAKkB,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eACjDnB,OAAA;UAAIkB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACN1B,OAAA;QAAKkB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BnB,OAAA;UAAOkB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpDnB,OAAA;YAAOkB,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC3BnB,OAAA;cAAAmB,QAAA,gBACEnB,OAAA;gBAAIkB,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3G1B,OAAA;gBAAIkB,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3G1B,OAAA;gBAAIkB,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzG1B,OAAA;gBAAIkB,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/G1B,OAAA;gBAAIkB,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR1B,OAAA;YAAOkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EACjDZ,WAAW,CAACa,GAAG,CAAEkB,OAAO,iBACvBtC,OAAA;cAAuBkB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBACjDnB,OAAA;gBAAIkB,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAAEmB,OAAO,CAAC9B;cAAI;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjG1B,OAAA;gBAAIkB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAEmB,OAAO,CAAC7B;cAAM;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvF1B,OAAA;gBAAIkB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAEmB,OAAO,CAAC5B;cAAK;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtF1B,OAAA;gBAAIkB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAEmB,OAAO,CAAC3B;cAAW;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5F1B,OAAA;gBAAIkB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,GAAEmB,OAAO,CAAC1B,WAAW,EAAC,GAAC;cAAA;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GALtFY,OAAO,CAAC9B,IAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMjB,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxB,EAAA,CAxIID,kBAAkB;AAAAsC,EAAA,GAAlBtC,kBAAkB;AA0IxB,eAAeA,kBAAkB;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}