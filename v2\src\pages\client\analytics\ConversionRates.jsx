import React, { useState } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';

const ConversionRates = () => {
  const [timeRange, setTimeRange] = useState('7d');

  // Sample data
  const conversionData = [
    { date: '2024-03-01', views: 1000, tryOns: 450, shares: 180, purchases: 45 },
    { date: '2024-03-02', views: 1200, tryOns: 520, shares: 210, purchases: 52 },
    { date: '2024-03-03', views: 1100, tryOns: 480, shares: 190, purchases: 48 },
    { date: '2024-03-04', views: 1300, tryOns: 580, shares: 230, purchases: 58 },
    { date: '2024-03-05', views: 1400, tryOns: 620, shares: 250, purchases: 62 },
    { date: '2024-03-06', views: 1500, tryOns: 680, shares: 270, purchases: 68 },
    { date: '2024-03-07', views: 1600, tryOns: 720, shares: 290, purchases: 72 },
  ];

  const funnelData = [
    { stage: 'Views', value: 9100, rate: '100%' },
    { stage: 'Try-Ons', value: 4050, rate: '44.5%' },
    { stage: 'Shares', value: 1620, rate: '17.8%' },
    { stage: 'Purchases', value: 405, rate: '4.5%' },
  ];

  const metrics = [
    {
      title: 'Overall Conversion Rate',
      value: '4.5%',
      change: '+0.8%',
      trend: 'up',
    },
    {
      title: 'Try-On to Purchase',
      value: '10.0%',
      change: '+1.2%',
      trend: 'up',
    },
    {
      title: 'Share to Purchase',
      value: '25.0%',
      change: '+2.5%',
      trend: 'up',
    },
    {
      title: 'Avg. Time to Purchase',
      value: '2.5 days',
      change: '-0.5 days',
      trend: 'down',
    },
  ];

  return (
    <div className="p-6">
      {/* Time Range Selector */}
      <div className="flex justify-end mb-6">
        <div className="inline-flex rounded-lg border border-gray-200 p-1">
          {['7d', '30d', '90d', '1y'].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                timeRange === range
                  ? 'bg-[#2D8C88] text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {metrics.map((metric) => (
          <div key={metric.title} className="bg-white rounded-xl shadow-sm p-6">
            <div>
              <p className="text-sm font-medium text-gray-600">{metric.title}</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">{metric.value}</p>
            </div>
            <div className="mt-4">
              <span className={`text-sm font-medium ${
                metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {metric.change}
              </span>
              <span className="text-sm text-gray-600 ml-2">from last period</span>
            </div>
          </div>
        ))}
      </div>

      {/* Conversion Funnel */}
      <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Conversion Funnel</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={funnelData} layout="vertical">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" />
              <YAxis dataKey="stage" type="category" />
              <Tooltip />
              <Bar dataKey="value" fill="#2D8C88" />
            </BarChart>
          </ResponsiveContainer>
        </div>
        <div className="mt-4 grid grid-cols-4 gap-4">
          {funnelData.map((stage) => (
            <div key={stage.stage} className="text-center">
              <p className="text-sm font-medium text-gray-600">{stage.stage}</p>
              <p className="text-lg font-semibold text-gray-900">{stage.value}</p>
              <p className="text-sm text-gray-500">{stage.rate}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Conversion Trends */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Conversion Trends</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={conversionData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="views"
                stroke="#2D8C88"
                strokeWidth={2}
                dot={{ fill: '#2D8C88' }}
              />
              <Line
                type="monotone"
                dataKey="tryOns"
                stroke="#3B82F6"
                strokeWidth={2}
                dot={{ fill: '#3B82F6' }}
              />
              <Line
                type="monotone"
                dataKey="shares"
                stroke="#10B981"
                strokeWidth={2}
                dot={{ fill: '#10B981' }}
              />
              <Line
                type="monotone"
                dataKey="purchases"
                stroke="#F59E0B"
                strokeWidth={2}
                dot={{ fill: '#F59E0B' }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default ConversionRates; 