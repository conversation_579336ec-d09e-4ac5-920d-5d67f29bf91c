{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\pages\\\\client\\\\analytics\\\\TimeAnalysis.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { <PERSON><PERSON>hart, Line, <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell } from 'recharts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TimeAnalysis = () => {\n  _s();\n  const [timeRange, setTimeRange] = useState('7d');\n\n  // Sample data\n  const hourlyData = [{\n    hour: '00:00',\n    sessions: 120,\n    tryOns: 45\n  }, {\n    hour: '02:00',\n    sessions: 80,\n    tryOns: 30\n  }, {\n    hour: '04:00',\n    sessions: 60,\n    tryOns: 20\n  }, {\n    hour: '06:00',\n    sessions: 100,\n    tryOns: 35\n  }, {\n    hour: '08:00',\n    sessions: 250,\n    tryOns: 90\n  }, {\n    hour: '10:00',\n    sessions: 400,\n    tryOns: 150\n  }, {\n    hour: '12:00',\n    sessions: 450,\n    tryOns: 180\n  }, {\n    hour: '14:00',\n    sessions: 380,\n    tryOns: 140\n  }, {\n    hour: '16:00',\n    sessions: 420,\n    tryOns: 160\n  }, {\n    hour: '18:00',\n    sessions: 350,\n    tryOns: 130\n  }, {\n    hour: '20:00',\n    sessions: 280,\n    tryOns: 100\n  }, {\n    hour: '22:00',\n    sessions: 150,\n    tryOns: 55\n  }];\n  const weeklyData = [{\n    day: 'Monday',\n    sessions: 1200,\n    tryOns: 450\n  }, {\n    day: 'Tuesday',\n    sessions: 1300,\n    tryOns: 480\n  }, {\n    day: 'Wednesday',\n    sessions: 1400,\n    tryOns: 520\n  }, {\n    day: 'Thursday',\n    sessions: 1350,\n    tryOns: 500\n  }, {\n    day: 'Friday',\n    sessions: 1500,\n    tryOns: 580\n  }, {\n    day: 'Saturday',\n    sessions: 1800,\n    tryOns: 720\n  }, {\n    day: 'Sunday',\n    sessions: 1600,\n    tryOns: 650\n  }];\n  const sessionDurationData = [{\n    range: '0-1 min',\n    value: 25\n  }, {\n    range: '1-3 min',\n    value: 35\n  }, {\n    range: '3-5 min',\n    value: 20\n  }, {\n    range: '5-10 min',\n    value: 15\n  }, {\n    range: '10+ min',\n    value: 5\n  }];\n  const COLORS = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'];\n  const metrics = [{\n    title: 'Peak Hour',\n    value: '12:00 PM',\n    change: 'No change',\n    trend: 'neutral'\n  }, {\n    title: 'Peak Day',\n    value: 'Saturday',\n    change: 'No change',\n    trend: 'neutral'\n  }, {\n    title: 'Avg. Session Duration',\n    value: '3m 45s',\n    change: '+15s',\n    trend: 'up'\n  }, {\n    title: 'Most Active Time',\n    value: '10:00 AM - 2:00 PM',\n    change: 'No change',\n    trend: 'neutral'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-end mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-flex rounded-lg border border-gray-200 p-1\",\n        children: ['7d', '30d', '90d', '1y'].map(range => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setTimeRange(range),\n          className: `px-3 py-1 text-sm font-medium rounded-md ${timeRange === range ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:text-gray-900'}`,\n          children: range\n        }, range, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n      children: metrics.map(metric => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm font-medium text-gray-600\",\n            children: metric.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-2xl font-semibold text-gray-900 mt-1\",\n            children: metric.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-sm font-medium ${metric.trend === 'up' ? 'text-green-600' : metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'}`,\n            children: metric.change\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600 ml-2\",\n            children: \"from last period\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 13\n        }, this)]\n      }, metric.title, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl shadow-sm p-6 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-4\",\n        children: \"Hourly Activity\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-80\",\n        children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: \"100%\",\n          children: /*#__PURE__*/_jsxDEV(LineChart, {\n            data: hourlyData,\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              dataKey: \"hour\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"sessions\",\n              stroke: \"#2D8C88\",\n              strokeWidth: 2,\n              dot: {\n                fill: '#2D8C88'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"tryOns\",\n              stroke: \"#3B82F6\",\n              strokeWidth: 2,\n              dot: {\n                fill: '#3B82F6'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Weekly Patterns\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-80\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: weeklyData,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"day\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"sessions\",\n                fill: \"#2D8C88\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"tryOns\",\n                fill: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Session Duration Distribution\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-80\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: sessionDurationData,\n                cx: \"50%\",\n                cy: \"50%\",\n                labelLine: false,\n                outerRadius: 80,\n                fill: \"#8884d8\",\n                dataKey: \"value\",\n                label: ({\n                  name,\n                  percent\n                }) => `${name} ${(percent * 100).toFixed(0)}%`,\n                children: sessionDurationData.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: COLORS[index % COLORS.length]\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n_s(TimeAnalysis, \"S/Xk/B2yitnwtU8ogBi58+n1JKE=\");\n_c = TimeAnalysis;\nexport default TimeAnalysis;\nvar _c;\n$RefreshReg$(_c, \"TimeAnalysis\");", "map": {"version": 3, "names": ["React", "useState", "Line<PERSON>hart", "Line", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "jsxDEV", "_jsxDEV", "TimeAnalysis", "_s", "timeRange", "setTimeRange", "hourlyData", "hour", "sessions", "tryOns", "weeklyData", "day", "sessionDurationData", "range", "value", "COLORS", "metrics", "title", "change", "trend", "className", "children", "map", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "metric", "width", "height", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "type", "stroke", "strokeWidth", "dot", "fill", "cx", "cy", "labelLine", "outerRadius", "label", "name", "percent", "toFixed", "entry", "index", "length", "_c", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/pages/client/analytics/TimeAnalysis.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON>,\r\n  <PERSON><PERSON>hart,\r\n  Bar,\r\n  XAxis,\r\n  YAxis,\r\n  CartesianGrid,\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON>,\r\n  Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON>,\r\n  <PERSON>,\r\n} from 'recharts';\r\n\r\nconst TimeAnalysis = () => {\r\n  const [timeRange, setTimeRange] = useState('7d');\r\n\r\n  // Sample data\r\n  const hourlyData = [\r\n    { hour: '00:00', sessions: 120, tryOns: 45 },\r\n    { hour: '02:00', sessions: 80, tryOns: 30 },\r\n    { hour: '04:00', sessions: 60, tryOns: 20 },\r\n    { hour: '06:00', sessions: 100, tryOns: 35 },\r\n    { hour: '08:00', sessions: 250, tryOns: 90 },\r\n    { hour: '10:00', sessions: 400, tryOns: 150 },\r\n    { hour: '12:00', sessions: 450, tryOns: 180 },\r\n    { hour: '14:00', sessions: 380, tryOns: 140 },\r\n    { hour: '16:00', sessions: 420, tryOns: 160 },\r\n    { hour: '18:00', sessions: 350, tryOns: 130 },\r\n    { hour: '20:00', sessions: 280, tryOns: 100 },\r\n    { hour: '22:00', sessions: 150, tryOns: 55 },\r\n  ];\r\n\r\n  const weeklyData = [\r\n    { day: 'Monday', sessions: 1200, tryOns: 450 },\r\n    { day: 'Tuesday', sessions: 1300, tryOns: 480 },\r\n    { day: 'Wednesday', sessions: 1400, tryOns: 520 },\r\n    { day: 'Thursday', sessions: 1350, tryOns: 500 },\r\n    { day: 'Friday', sessions: 1500, tryOns: 580 },\r\n    { day: 'Saturday', sessions: 1800, tryOns: 720 },\r\n    { day: 'Sunday', sessions: 1600, tryOns: 650 },\r\n  ];\r\n\r\n  const sessionDurationData = [\r\n    { range: '0-1 min', value: 25 },\r\n    { range: '1-3 min', value: 35 },\r\n    { range: '3-5 min', value: 20 },\r\n    { range: '5-10 min', value: 15 },\r\n    { range: '10+ min', value: 5 },\r\n  ];\r\n\r\n  const COLORS = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'];\r\n\r\n  const metrics = [\r\n    {\r\n      title: 'Peak Hour',\r\n      value: '12:00 PM',\r\n      change: 'No change',\r\n      trend: 'neutral',\r\n    },\r\n    {\r\n      title: 'Peak Day',\r\n      value: 'Saturday',\r\n      change: 'No change',\r\n      trend: 'neutral',\r\n    },\r\n    {\r\n      title: 'Avg. Session Duration',\r\n      value: '3m 45s',\r\n      change: '+15s',\r\n      trend: 'up',\r\n    },\r\n    {\r\n      title: 'Most Active Time',\r\n      value: '10:00 AM - 2:00 PM',\r\n      change: 'No change',\r\n      trend: 'neutral',\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-6\">\r\n      {/* Time Range Selector */}\r\n      <div className=\"flex justify-end mb-6\">\r\n        <div className=\"inline-flex rounded-lg border border-gray-200 p-1\">\r\n          {['7d', '30d', '90d', '1y'].map((range) => (\r\n            <button\r\n              key={range}\r\n              onClick={() => setTimeRange(range)}\r\n              className={`px-3 py-1 text-sm font-medium rounded-md ${\r\n                timeRange === range\r\n                  ? 'bg-[#2D8C88] text-white'\r\n                  : 'text-gray-600 hover:text-gray-900'\r\n              }`}\r\n            >\r\n              {range}\r\n            </button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Key Metrics */}\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\r\n        {metrics.map((metric) => (\r\n          <div key={metric.title} className=\"bg-white rounded-xl shadow-sm p-6\">\r\n            <div>\r\n              <p className=\"text-sm font-medium text-gray-600\">{metric.title}</p>\r\n              <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{metric.value}</p>\r\n            </div>\r\n            <div className=\"mt-4\">\r\n              <span className={`text-sm font-medium ${\r\n                metric.trend === 'up' ? 'text-green-600' : \r\n                metric.trend === 'down' ? 'text-red-600' : \r\n                'text-gray-600'\r\n              }`}>\r\n                {metric.change}\r\n              </span>\r\n              <span className=\"text-sm text-gray-600 ml-2\">from last period</span>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Hourly Activity */}\r\n      <div className=\"bg-white rounded-xl shadow-sm p-6 mb-6\">\r\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Hourly Activity</h3>\r\n        <div className=\"h-80\">\r\n          <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n            <LineChart data={hourlyData}>\r\n              <CartesianGrid strokeDasharray=\"3 3\" />\r\n              <XAxis dataKey=\"hour\" />\r\n              <YAxis />\r\n              <Tooltip />\r\n              <Line\r\n                type=\"monotone\"\r\n                dataKey=\"sessions\"\r\n                stroke=\"#2D8C88\"\r\n                strokeWidth={2}\r\n                dot={{ fill: '#2D8C88' }}\r\n              />\r\n              <Line\r\n                type=\"monotone\"\r\n                dataKey=\"tryOns\"\r\n                stroke=\"#3B82F6\"\r\n                strokeWidth={2}\r\n                dot={{ fill: '#3B82F6' }}\r\n              />\r\n            </LineChart>\r\n          </ResponsiveContainer>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Weekly Patterns */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\r\n        <div className=\"bg-white rounded-xl shadow-sm p-6\">\r\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Weekly Patterns</h3>\r\n          <div className=\"h-80\">\r\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n              <BarChart data={weeklyData}>\r\n                <CartesianGrid strokeDasharray=\"3 3\" />\r\n                <XAxis dataKey=\"day\" />\r\n                <YAxis />\r\n                <Tooltip />\r\n                <Bar dataKey=\"sessions\" fill=\"#2D8C88\" />\r\n                <Bar dataKey=\"tryOns\" fill=\"#3B82F6\" />\r\n              </BarChart>\r\n            </ResponsiveContainer>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white rounded-xl shadow-sm p-6\">\r\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Session Duration Distribution</h3>\r\n          <div className=\"h-80\">\r\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n              <PieChart>\r\n                <Pie\r\n                  data={sessionDurationData}\r\n                  cx=\"50%\"\r\n                  cy=\"50%\"\r\n                  labelLine={false}\r\n                  outerRadius={80}\r\n                  fill=\"#8884d8\"\r\n                  dataKey=\"value\"\r\n                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\r\n                >\r\n                  {sessionDurationData.map((entry, index) => (\r\n                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\r\n                  ))}\r\n                </Pie>\r\n                <Tooltip />\r\n              </PieChart>\r\n            </ResponsiveContainer>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TimeAnalysis; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,EACnBC,QAAQ,EACRC,GAAG,EACHC,IAAI,QACC,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAMoB,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAG,CAAC,EAC5C;IAAEF,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,EAAE;IAAEC,MAAM,EAAE;EAAG,CAAC,EAC3C;IAAEF,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,EAAE;IAAEC,MAAM,EAAE;EAAG,CAAC,EAC3C;IAAEF,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAG,CAAC,EAC5C;IAAEF,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAG,CAAC,EAC5C;IAAEF,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC7C;IAAEF,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC7C;IAAEF,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC7C;IAAEF,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC7C;IAAEF,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC7C;IAAEF,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC7C;IAAEF,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAG,CAAC,CAC7C;EAED,MAAMC,UAAU,GAAG,CACjB;IAAEC,GAAG,EAAE,QAAQ;IAAEH,QAAQ,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC9C;IAAEE,GAAG,EAAE,SAAS;IAAEH,QAAQ,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC/C;IAAEE,GAAG,EAAE,WAAW;IAAEH,QAAQ,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,EACjD;IAAEE,GAAG,EAAE,UAAU;IAAEH,QAAQ,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,EAChD;IAAEE,GAAG,EAAE,QAAQ;IAAEH,QAAQ,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC9C;IAAEE,GAAG,EAAE,UAAU;IAAEH,QAAQ,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,EAChD;IAAEE,GAAG,EAAE,QAAQ;IAAEH,QAAQ,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,CAC/C;EAED,MAAMG,mBAAmB,GAAG,CAC1B;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAG,CAAC,EAC/B;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAG,CAAC,EAC/B;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAG,CAAC,EAC/B;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAG,CAAC,EAChC;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAE,CAAC,CAC/B;EAED,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAEtE,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,WAAW;IAClBH,KAAK,EAAE,UAAU;IACjBI,MAAM,EAAE,WAAW;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,UAAU;IACjBH,KAAK,EAAE,UAAU;IACjBI,MAAM,EAAE,WAAW;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,uBAAuB;IAC9BH,KAAK,EAAE,QAAQ;IACfI,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,kBAAkB;IACzBH,KAAK,EAAE,oBAAoB;IAC3BI,MAAM,EAAE,WAAW;IACnBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACElB,OAAA;IAAKmB,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAElBpB,OAAA;MAAKmB,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpCpB,OAAA;QAAKmB,SAAS,EAAC,mDAAmD;QAAAC,QAAA,EAC/D,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAACC,GAAG,CAAET,KAAK,iBACpCZ,OAAA;UAEEsB,OAAO,EAAEA,CAAA,KAAMlB,YAAY,CAACQ,KAAK,CAAE;UACnCO,SAAS,EAAE,4CACThB,SAAS,KAAKS,KAAK,GACf,yBAAyB,GACzB,mCAAmC,EACtC;UAAAQ,QAAA,EAEFR;QAAK,GARDA,KAAK;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASJ,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAKmB,SAAS,EAAC,2DAA2D;MAAAC,QAAA,EACvEL,OAAO,CAACM,GAAG,CAAEM,MAAM,iBAClB3B,OAAA;QAAwBmB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBACnEpB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA;YAAGmB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAEO,MAAM,CAACX;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnE1B,OAAA;YAAGmB,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAEO,MAAM,CAACd;UAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,eACN1B,OAAA;UAAKmB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBpB,OAAA;YAAMmB,SAAS,EAAE,uBACfQ,MAAM,CAACT,KAAK,KAAK,IAAI,GAAG,gBAAgB,GACxCS,MAAM,CAACT,KAAK,KAAK,MAAM,GAAG,cAAc,GACxC,eAAe,EACd;YAAAE,QAAA,EACAO,MAAM,CAACV;UAAM;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACP1B,OAAA;YAAMmB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAgB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA,GAdEC,MAAM,CAACX,KAAK;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAejB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN1B,OAAA;MAAKmB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDpB,OAAA;QAAImB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAe;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3E1B,OAAA;QAAKmB,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBpB,OAAA,CAACL,mBAAmB;UAACiC,KAAK,EAAC,MAAM;UAACC,MAAM,EAAC,MAAM;UAAAT,QAAA,eAC7CpB,OAAA,CAACd,SAAS;YAAC4C,IAAI,EAAEzB,UAAW;YAAAe,QAAA,gBAC1BpB,OAAA,CAACR,aAAa;cAACuC,eAAe,EAAC;YAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvC1B,OAAA,CAACV,KAAK;cAAC0C,OAAO,EAAC;YAAM;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxB1B,OAAA,CAACT,KAAK;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACT1B,OAAA,CAACP,OAAO;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX1B,OAAA,CAACb,IAAI;cACH8C,IAAI,EAAC,UAAU;cACfD,OAAO,EAAC,UAAU;cAClBE,MAAM,EAAC,SAAS;cAChBC,WAAW,EAAE,CAAE;cACfC,GAAG,EAAE;gBAAEC,IAAI,EAAE;cAAU;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACF1B,OAAA,CAACb,IAAI;cACH8C,IAAI,EAAC,UAAU;cACfD,OAAO,EAAC,QAAQ;cAChBE,MAAM,EAAC,SAAS;cAChBC,WAAW,EAAE,CAAE;cACfC,GAAG,EAAE;gBAAEC,IAAI,EAAE;cAAU;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAKmB,SAAS,EAAC,4CAA4C;MAAAC,QAAA,gBACzDpB,OAAA;QAAKmB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDpB,OAAA;UAAImB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3E1B,OAAA;UAAKmB,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBpB,OAAA,CAACL,mBAAmB;YAACiC,KAAK,EAAC,MAAM;YAACC,MAAM,EAAC,MAAM;YAAAT,QAAA,eAC7CpB,OAAA,CAACZ,QAAQ;cAAC0C,IAAI,EAAErB,UAAW;cAAAW,QAAA,gBACzBpB,OAAA,CAACR,aAAa;gBAACuC,eAAe,EAAC;cAAK;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC1B,OAAA,CAACV,KAAK;gBAAC0C,OAAO,EAAC;cAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvB1B,OAAA,CAACT,KAAK;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT1B,OAAA,CAACP,OAAO;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX1B,OAAA,CAACX,GAAG;gBAAC2C,OAAO,EAAC,UAAU;gBAACK,IAAI,EAAC;cAAS;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC1B,OAAA,CAACX,GAAG;gBAAC2C,OAAO,EAAC,QAAQ;gBAACK,IAAI,EAAC;cAAS;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1B,OAAA;QAAKmB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDpB,OAAA;UAAImB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAA6B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzF1B,OAAA;UAAKmB,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBpB,OAAA,CAACL,mBAAmB;YAACiC,KAAK,EAAC,MAAM;YAACC,MAAM,EAAC,MAAM;YAAAT,QAAA,eAC7CpB,OAAA,CAACJ,QAAQ;cAAAwB,QAAA,gBACPpB,OAAA,CAACH,GAAG;gBACFiC,IAAI,EAAEnB,mBAAoB;gBAC1B2B,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACRC,SAAS,EAAE,KAAM;gBACjBC,WAAW,EAAE,EAAG;gBAChBJ,IAAI,EAAC,SAAS;gBACdL,OAAO,EAAC,OAAO;gBACfU,KAAK,EAAEA,CAAC;kBAAEC,IAAI;kBAAEC;gBAAQ,CAAC,KAAK,GAAGD,IAAI,IAAI,CAACC,OAAO,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAI;gBAAAzB,QAAA,EAEtET,mBAAmB,CAACU,GAAG,CAAC,CAACyB,KAAK,EAAEC,KAAK,kBACpC/C,OAAA,CAACF,IAAI;kBAAuBuC,IAAI,EAAEvB,MAAM,CAACiC,KAAK,GAAGjC,MAAM,CAACkC,MAAM;gBAAE,GAArD,QAAQD,KAAK,EAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAwC,CACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1B,OAAA,CAACP,OAAO;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxB,EAAA,CAvLID,YAAY;AAAAgD,EAAA,GAAZhD,YAAY;AAyLlB,eAAeA,YAAY;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}