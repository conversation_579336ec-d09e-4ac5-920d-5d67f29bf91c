import React, { useState } from 'react';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
} from 'recharts';

const GeographicData = () => {
  const [timeRange, setTimeRange] = useState('7d');

  // Sample data
  const countryData = [
    { name: 'United States', value: 45 },
    { name: 'United Kingdom', value: 15 },
    { name: 'Germany', value: 10 },
    { name: 'France', value: 8 },
    { name: 'Canada', value: 7 },
    { name: 'Australia', value: 5 },
    { name: 'Other', value: 10 },
  ];

  const regionData = [
    { name: 'North America', value: 52 },
    { name: 'Europe', value: 33 },
    { name: 'Asia Pacific', value: 8 },
    { name: 'South America', value: 4 },
    { name: 'Africa', value: 3 },
  ];

  const cityData = [
    { city: 'New York', users: 1200, tryOns: 450 },
    { city: 'London', users: 900, tryOns: 350 },
    { city: 'Berlin', users: 800, tryOns: 300 },
    { city: 'Paris', users: 700, tryOns: 250 },
    { city: 'Toronto', users: 600, tryOns: 200 },
    { city: 'Sydney', users: 500, tryOns: 180 },
  ];

  const COLORS = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#EC4899'];

  const metrics = [
    {
      title: 'Total Countries',
      value: '45',
      change: '+3',
      trend: 'up',
    },
    {
      title: 'Top Country',
      value: 'United States',
      change: 'No change',
      trend: 'neutral',
    },
    {
      title: 'Top City',
      value: 'New York',
      change: 'No change',
      trend: 'neutral',
    },
    {
      title: 'Avg. Users/Country',
      value: '2,500',
      change: '+150',
      trend: 'up',
    },
  ];

  return (
    <div className="p-6">
      {/* Time Range Selector */}
      <div className="flex justify-end mb-6">
        <div className="inline-flex rounded-lg border border-gray-200 p-1">
          {['7d', '30d', '90d', '1y'].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                timeRange === range
                  ? 'bg-[#2D8C88] text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {metrics.map((metric) => (
          <div key={metric.title} className="bg-white rounded-xl shadow-sm p-6">
            <div>
              <p className="text-sm font-medium text-gray-600">{metric.title}</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">{metric.value}</p>
            </div>
            <div className="mt-4">
              <span className={`text-sm font-medium ${
                metric.trend === 'up' ? 'text-green-600' : 
                metric.trend === 'down' ? 'text-red-600' : 
                'text-gray-600'
              }`}>
                {metric.change}
              </span>
              <span className="text-sm text-gray-600 ml-2">from last period</span>
            </div>
          </div>
        ))}
      </div>

      {/* Geographic Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Top Countries</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={countryData} layout="vertical">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="name" type="category" />
                <Tooltip />
                <Bar dataKey="value" fill="#2D8C88" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Regional Distribution</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={regionData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {regionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Top Cities */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Top Cities</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={cityData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="city" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="users" fill="#2D8C88" name="Users" />
              <Bar dataKey="tryOns" fill="#3B82F6" name="Try-Ons" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default GeographicData; 