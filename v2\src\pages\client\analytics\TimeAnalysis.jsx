import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'recharts';

const TimeAnalysis = () => {
  const [timeRange, setTimeRange] = useState('7d');

  // Sample data
  const hourlyData = [
    { hour: '00:00', sessions: 120, tryOns: 45 },
    { hour: '02:00', sessions: 80, tryOns: 30 },
    { hour: '04:00', sessions: 60, tryOns: 20 },
    { hour: '06:00', sessions: 100, tryOns: 35 },
    { hour: '08:00', sessions: 250, tryOns: 90 },
    { hour: '10:00', sessions: 400, tryOns: 150 },
    { hour: '12:00', sessions: 450, tryOns: 180 },
    { hour: '14:00', sessions: 380, tryOns: 140 },
    { hour: '16:00', sessions: 420, tryOns: 160 },
    { hour: '18:00', sessions: 350, tryOns: 130 },
    { hour: '20:00', sessions: 280, tryOns: 100 },
    { hour: '22:00', sessions: 150, tryOns: 55 },
  ];

  const weeklyData = [
    { day: 'Monday', sessions: 1200, tryOns: 450 },
    { day: 'Tuesday', sessions: 1300, tryOns: 480 },
    { day: 'Wednesday', sessions: 1400, tryOns: 520 },
    { day: 'Thursday', sessions: 1350, tryOns: 500 },
    { day: 'Friday', sessions: 1500, tryOns: 580 },
    { day: 'Saturday', sessions: 1800, tryOns: 720 },
    { day: 'Sunday', sessions: 1600, tryOns: 650 },
  ];

  const sessionDurationData = [
    { range: '0-1 min', value: 25 },
    { range: '1-3 min', value: 35 },
    { range: '3-5 min', value: 20 },
    { range: '5-10 min', value: 15 },
    { range: '10+ min', value: 5 },
  ];

  const COLORS = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'];

  const metrics = [
    {
      title: 'Peak Hour',
      value: '12:00 PM',
      change: 'No change',
      trend: 'neutral',
    },
    {
      title: 'Peak Day',
      value: 'Saturday',
      change: 'No change',
      trend: 'neutral',
    },
    {
      title: 'Avg. Session Duration',
      value: '3m 45s',
      change: '+15s',
      trend: 'up',
    },
    {
      title: 'Most Active Time',
      value: '10:00 AM - 2:00 PM',
      change: 'No change',
      trend: 'neutral',
    },
  ];

  return (
    <div className="p-6">
      {/* Time Range Selector */}
      <div className="flex justify-end mb-6">
        <div className="inline-flex rounded-lg border border-gray-200 p-1">
          {['7d', '30d', '90d', '1y'].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                timeRange === range
                  ? 'bg-[#2D8C88] text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {metrics.map((metric) => (
          <div key={metric.title} className="bg-white rounded-xl shadow-sm p-6">
            <div>
              <p className="text-sm font-medium text-gray-600">{metric.title}</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">{metric.value}</p>
            </div>
            <div className="mt-4">
              <span className={`text-sm font-medium ${
                metric.trend === 'up' ? 'text-green-600' : 
                metric.trend === 'down' ? 'text-red-600' : 
                'text-gray-600'
              }`}>
                {metric.change}
              </span>
              <span className="text-sm text-gray-600 ml-2">from last period</span>
            </div>
          </div>
        ))}
      </div>

      {/* Hourly Activity */}
      <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Hourly Activity</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={hourlyData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="hour" />
              <YAxis />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="sessions"
                stroke="#2D8C88"
                strokeWidth={2}
                dot={{ fill: '#2D8C88' }}
              />
              <Line
                type="monotone"
                dataKey="tryOns"
                stroke="#3B82F6"
                strokeWidth={2}
                dot={{ fill: '#3B82F6' }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Weekly Patterns */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Weekly Patterns</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={weeklyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="sessions" fill="#2D8C88" />
                <Bar dataKey="tryOns" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Session Duration Distribution</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={sessionDurationData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {sessionDurationData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TimeAnalysis; 