import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  <PERSON><PERSON>hart,
  <PERSON>,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';

const UserEngagement = () => {
  const [timeRange, setTimeRange] = useState('7d');

  // Sample data
  const engagementData = [
    { date: '2024-03-01', sessions: 120, avgDuration: 180, interactions: 450 },
    { date: '2024-03-02', sessions: 150, avgDuration: 185, interactions: 480 },
    { date: '2024-03-03', sessions: 180, avgDuration: 190, interactions: 520 },
    { date: '2024-03-04', sessions: 160, avgDuration: 175, interactions: 460 },
    { date: '2024-03-05', sessions: 200, avgDuration: 195, interactions: 580 },
    { date: '2024-03-06', sessions: 220, avgDuration: 200, interactions: 620 },
    { date: '2024-03-07', sessions: 250, avgDuration: 205, interactions: 680 },
  ];

  const interactionTypes = [
    { name: 'Product Rotation', value: 35 },
    { name: 'Color Change', value: 25 },
    { name: 'Size Adjustment', value: 20 },
    { name: 'Share', value: 15 },
    { name: 'Other', value: 5 },
  ];

  const COLORS = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'];

  const metrics = [
    {
      title: 'Total Sessions',
      value: '1,280',
      change: '+15%',
      trend: 'up',
    },
    {
      title: 'Avg. Session Duration',
      value: '3m 45s',
      change: '+8%',
      trend: 'up',
    },
    {
      title: 'Total Interactions',
      value: '3,790',
      change: '+12%',
      trend: 'up',
    },
    {
      title: 'Avg. Interactions/Session',
      value: '2.96',
      change: '+5%',
      trend: 'up',
    },
  ];

  return (
    <div className="p-6">
      {/* Time Range Selector */}
      <div className="flex justify-end mb-6">
        <div className="inline-flex rounded-lg border border-gray-200 p-1">
          {['7d', '30d', '90d', '1y'].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                timeRange === range
                  ? 'bg-[#2D8C88] text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {metrics.map((metric) => (
          <div key={metric.title} className="bg-white rounded-xl shadow-sm p-6">
            <div>
              <p className="text-sm font-medium text-gray-600">{metric.title}</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">{metric.value}</p>
            </div>
            <div className="mt-4">
              <span className={`text-sm font-medium ${
                metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {metric.change}
              </span>
              <span className="text-sm text-gray-600 ml-2">from last period</span>
            </div>
          </div>
        ))}
      </div>

      {/* Engagement Trends */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Session Trends</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={engagementData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="sessions"
                  stroke="#2D8C88"
                  strokeWidth={2}
                  dot={{ fill: '#2D8C88' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Interaction Types</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={interactionTypes}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {interactionTypes.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Detailed Metrics */}
      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Detailed Metrics</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sessions</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Duration</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Interactions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {engagementData.map((day) => (
                <tr key={day.date} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{day.date}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{day.sessions}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{day.avgDuration}s</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{day.interactions}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default UserEngagement; 