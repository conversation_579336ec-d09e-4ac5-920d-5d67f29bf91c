[{"D:\\Via\\New folder\\v2\\src\\index.js": "1", "D:\\Via\\New folder\\v2\\src\\App.js": "2", "D:\\Via\\New folder\\v2\\src\\reportWebVitals.js": "3", "D:\\Via\\New folder\\v2\\src\\pages\\Login.jsx": "4", "D:\\Via\\New folder\\v2\\src\\pages\\Watches.jsx": "5", "D:\\Via\\New folder\\v2\\src\\pages\\Bracelets.jsx": "6", "D:\\Via\\New folder\\v2\\src\\pages\\HowItWorks.jsx": "7", "D:\\Via\\New folder\\v2\\src\\pages\\Home.jsx": "8", "D:\\Via\\New folder\\v2\\src\\pages\\VirtualTryOn.jsx": "9", "D:\\Via\\New folder\\v2\\src\\pages\\WhyViaTryon.jsx": "10", "D:\\Via\\New folder\\v2\\src\\pages\\SearchResults.jsx": "11", "D:\\Via\\New folder\\v2\\src\\pages\\ProductDetails.jsx": "12", "D:\\Via\\New folder\\v2\\src\\pages\\Contact.jsx": "13", "D:\\Via\\New folder\\v2\\src\\pages\\Requirements.jsx": "14", "D:\\Via\\New folder\\v2\\src\\components\\DemoForm.jsx": "15", "D:\\Via\\New folder\\v2\\src\\components\\Footer.jsx": "16", "D:\\Via\\New folder\\v2\\src\\components\\Navbar.jsx": "17", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\Clients.jsx": "18", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\TryOnAnalytics.jsx": "19", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\AdminDashboard.jsx": "20", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\Settings.jsx": "21", "D:\\Via\\New folder\\v2\\src\\pages\\client\\ClientDashboard.jsx": "22", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ClientAnalytics.jsx": "23", "D:\\Via\\New folder\\v2\\src\\utils\\imageLoader.js": "24", "D:\\Via\\New folder\\v2\\src\\services\\api.js": "25", "D:\\Via\\New folder\\v2\\src\\data\\productCollections.js": "26", "D:\\Via\\New folder\\v2\\src\\context\\CartContext.js": "27", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\UserEngagement.jsx": "28", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\TimeAnalysis.jsx": "29", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\Overview.jsx": "30", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ConversionRates.jsx": "31", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ProductPerformance.jsx": "32", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\DeviceStats.jsx": "33", "D:\\Via\\New folder\\v2\\src\\components\\admin\\AdminNavbar.jsx": "34", "D:\\Via\\New folder\\v2\\src\\components\\admin\\AdminSidebar.jsx": "35", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\GeographicData.jsx": "36", "D:\\Via\\New folder\\v2\\src\\components\\client\\ClientSidebar.jsx": "37", "D:\\Via\\New folder\\v2\\src\\components\\client\\ClientNavbar.jsx": "38", "D:\\Via\\New folder\\v2\\src\\utils\\backgroundRemover.js": "39", "D:\\Via\\New folder\\v2\\src\\components\\EmbedCodeGenerator.jsx": "40"}, {"size": 653, "mtime": 1749196504388, "results": "41", "hashOfConfig": "42"}, {"size": 4525, "mtime": 1749204652036, "results": "43", "hashOfConfig": "42"}, {"size": 362, "mtime": 1746042995461, "results": "44", "hashOfConfig": "42"}, {"size": 7675, "mtime": 1749196504239, "results": "45", "hashOfConfig": "42"}, {"size": 19373, "mtime": 1748874413890, "results": "46", "hashOfConfig": "42"}, {"size": 19487, "mtime": 1748874429932, "results": "47", "hashOfConfig": "42"}, {"size": 17189, "mtime": 1748864169684, "results": "48", "hashOfConfig": "42"}, {"size": 41162, "mtime": 1748975550119, "results": "49", "hashOfConfig": "42"}, {"size": 60392, "mtime": 1748605437244, "results": "50", "hashOfConfig": "42"}, {"size": 16901, "mtime": 1748864143496, "results": "51", "hashOfConfig": "42"}, {"size": 6108, "mtime": 1746344994148, "results": "52", "hashOfConfig": "42"}, {"size": 16294, "mtime": 1748293996467, "results": "53", "hashOfConfig": "42"}, {"size": 25359, "mtime": 1748867397685, "results": "54", "hashOfConfig": "42"}, {"size": 7100, "mtime": 1748867056681, "results": "55", "hashOfConfig": "42"}, {"size": 11616, "mtime": 1746342271795, "results": "56", "hashOfConfig": "42"}, {"size": 6479, "mtime": 1748866960444, "results": "57", "hashOfConfig": "42"}, {"size": 14287, "mtime": 1749152911428, "results": "58", "hashOfConfig": "42"}, {"size": 20074, "mtime": 1749203894531, "results": "59", "hashOfConfig": "42"}, {"size": 12445, "mtime": 1749144139761, "results": "60", "hashOfConfig": "42"}, {"size": 17374, "mtime": 1749203517435, "results": "61", "hashOfConfig": "42"}, {"size": 12062, "mtime": 1749144136782, "results": "62", "hashOfConfig": "42"}, {"size": 17601, "mtime": 1749204008438, "results": "63", "hashOfConfig": "42"}, {"size": 3734, "mtime": 1749198331118, "results": "64", "hashOfConfig": "42"}, {"size": 8174, "mtime": 1748283061371, "results": "65", "hashOfConfig": "42"}, {"size": 1190, "mtime": 1748291506520, "results": "66", "hashOfConfig": "42"}, {"size": 10601, "mtime": 1748277235110, "results": "67", "hashOfConfig": "42"}, {"size": 4297, "mtime": 1748283089634, "results": "68", "hashOfConfig": "42"}, {"size": 6733, "mtime": 1749196502414, "results": "69", "hashOfConfig": "42"}, {"size": 6636, "mtime": 1749198338414, "results": "70", "hashOfConfig": "42"}, {"size": 8376, "mtime": 1749196500851, "results": "71", "hashOfConfig": "42"}, {"size": 5620, "mtime": 1749196502795, "results": "72", "hashOfConfig": "42"}, {"size": 6238, "mtime": 1749196501461, "results": "73", "hashOfConfig": "42"}, {"size": 6156, "mtime": 1749196502910, "results": "74", "hashOfConfig": "42"}, {"size": 8877, "mtime": 1749150209342, "results": "75", "hashOfConfig": "42"}, {"size": 7144, "mtime": 1749144357974, "results": "76", "hashOfConfig": "42"}, {"size": 5658, "mtime": 1749196503295, "results": "77", "hashOfConfig": "42"}, {"size": 8337, "mtime": 1749196503594, "results": "78", "hashOfConfig": "42"}, {"size": 8526, "mtime": 1749205011761, "results": "79", "hashOfConfig": "42"}, {"size": 5096, "mtime": 1748291546769, "results": "80", "hashOfConfig": "42"}, {"size": 9697, "mtime": 1749203944731, "results": "81", "hashOfConfig": "42"}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gdfhta", {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Via\\New folder\\v2\\src\\index.js", [], [], "D:\\Via\\New folder\\v2\\src\\App.js", ["202"], [], "D:\\Via\\New folder\\v2\\src\\reportWebVitals.js", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Login.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Watches.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Bracelets.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\HowItWorks.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Home.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\VirtualTryOn.jsx", ["203"], [], "D:\\Via\\New folder\\v2\\src\\pages\\WhyViaTryon.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\SearchResults.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\ProductDetails.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Contact.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Requirements.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\DemoForm.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\Footer.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\Navbar.jsx", ["204"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\Clients.jsx", ["205"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\TryOnAnalytics.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\AdminDashboard.jsx", ["206", "207", "208", "209", "210", "211", "212"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\Settings.jsx", ["213", "214"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\ClientDashboard.jsx", ["215", "216", "217"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ClientAnalytics.jsx", ["218", "219"], [], "D:\\Via\\New folder\\v2\\src\\utils\\imageLoader.js", [], [], "D:\\Via\\New folder\\v2\\src\\services\\api.js", [], [], "D:\\Via\\New folder\\v2\\src\\data\\productCollections.js", [], [], "D:\\Via\\New folder\\v2\\src\\context\\CartContext.js", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\UserEngagement.jsx", ["220", "221", "222"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\TimeAnalysis.jsx", ["223"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\Overview.jsx", ["224"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ConversionRates.jsx", ["225"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ProductPerformance.jsx", ["226"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\DeviceStats.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\admin\\AdminNavbar.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\admin\\AdminSidebar.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\GeographicData.jsx", ["227"], [], "D:\\Via\\New folder\\v2\\src\\components\\client\\ClientSidebar.jsx", ["228"], [], "D:\\Via\\New folder\\v2\\src\\components\\client\\ClientNavbar.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\utils\\backgroundRemover.js", [], [], "D:\\Via\\New folder\\v2\\src\\components\\EmbedCodeGenerator.jsx", ["229", "230", "231"], [], {"ruleId": "232", "severity": 1, "message": "233", "line": 40, "column": 9, "nodeType": "234", "messageId": "235", "endLine": 40, "endColumn": 13}, {"ruleId": "236", "severity": 1, "message": "237", "line": 203, "column": 6, "nodeType": "238", "endLine": 203, "endColumn": 16, "suggestions": "239"}, {"ruleId": "232", "severity": 1, "message": "240", "line": 37, "column": 9, "nodeType": "234", "messageId": "235", "endLine": 37, "endColumn": 17}, {"ruleId": "232", "severity": 1, "message": "241", "line": 5, "column": 10, "nodeType": "234", "messageId": "235", "endLine": 5, "endColumn": 16}, {"ruleId": "232", "severity": 1, "message": "242", "line": 1, "column": 27, "nodeType": "234", "messageId": "235", "endLine": 1, "endColumn": 36}, {"ruleId": "232", "severity": 1, "message": "243", "line": 5, "column": 27, "nodeType": "234", "messageId": "235", "endLine": 5, "endColumn": 35}, {"ruleId": "232", "severity": 1, "message": "244", "line": 5, "column": 37, "nodeType": "234", "messageId": "235", "endLine": 5, "endColumn": 40}, {"ruleId": "232", "severity": 1, "message": "245", "line": 6, "column": 48, "nodeType": "234", "messageId": "235", "endLine": 6, "endColumn": 53}, {"ruleId": "232", "severity": 1, "message": "246", "line": 6, "column": 62, "nodeType": "234", "messageId": "235", "endLine": 6, "endColumn": 72}, {"ruleId": "232", "severity": 1, "message": "247", "line": 6, "column": 74, "nodeType": "234", "messageId": "235", "endLine": 6, "endColumn": 81}, {"ruleId": "232", "severity": 1, "message": "248", "line": 12, "column": 25, "nodeType": "234", "messageId": "235", "endLine": 12, "endColumn": 41}, {"ruleId": "232", "severity": 1, "message": "249", "line": 4, "column": 10, "nodeType": "234", "messageId": "235", "endLine": 4, "endColumn": 16}, {"ruleId": "232", "severity": 1, "message": "250", "line": 26, "column": 9, "nodeType": "234", "messageId": "235", "endLine": 26, "endColumn": 26}, {"ruleId": "232", "severity": 1, "message": "242", "line": 1, "column": 27, "nodeType": "234", "messageId": "235", "endLine": 1, "endColumn": 36}, {"ruleId": "232", "severity": 1, "message": "251", "line": 7, "column": 61, "nodeType": "234", "messageId": "235", "endLine": 7, "endColumn": 66}, {"ruleId": "232", "severity": 1, "message": "252", "line": 14, "column": 22, "nodeType": "234", "messageId": "235", "endLine": 14, "endColumn": 35}, {"ruleId": "232", "severity": 1, "message": "242", "line": 1, "column": 27, "nodeType": "234", "messageId": "235", "endLine": 1, "endColumn": 36}, {"ruleId": "232", "severity": 1, "message": "253", "line": 1, "column": 38, "nodeType": "234", "messageId": "235", "endLine": 1, "endColumn": 44}, {"ruleId": "232", "severity": 1, "message": "243", "line": 5, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 5, "endColumn": 11}, {"ruleId": "232", "severity": 1, "message": "244", "line": 6, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 6, "endColumn": 6}, {"ruleId": "232", "severity": 1, "message": "254", "line": 14, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 14, "endColumn": 9}, {"ruleId": "232", "severity": 1, "message": "254", "line": 11, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 11, "endColumn": 9}, {"ruleId": "232", "severity": 1, "message": "254", "line": 14, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 14, "endColumn": 9}, {"ruleId": "232", "severity": 1, "message": "254", "line": 11, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 11, "endColumn": 9}, {"ruleId": "232", "severity": 1, "message": "254", "line": 11, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 11, "endColumn": 9}, {"ruleId": "232", "severity": 1, "message": "254", "line": 9, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 9, "endColumn": 9}, {"ruleId": "232", "severity": 1, "message": "255", "line": 1, "column": 17, "nodeType": "234", "messageId": "235", "endLine": 1, "endColumn": 25}, {"ruleId": "232", "severity": 1, "message": "256", "line": 3, "column": 23, "nodeType": "234", "messageId": "235", "endLine": 3, "endColumn": 27}, {"ruleId": "257", "severity": 1, "message": "258", "line": 197, "column": 31, "nodeType": "259", "endLine": 197, "endColumn": 65}, {"ruleId": "257", "severity": 1, "message": "258", "line": 198, "column": 21, "nodeType": "259", "endLine": 198, "endColumn": 60}, "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'activeCategory'. Either include it or remove the dependency array.", "ArrayExpression", ["260"], "'isClient' is assigned a value but never used.", "'Search' is defined but never used.", "'useEffect' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Clock' is defined but never used.", "'Smartphone' is defined but never used.", "'Monitor' is defined but never used.", "'setDashboardData' is assigned a value but never used.", "'motion' is defined but never used.", "'handleInputChange' is assigned a value but never used.", "'Globe' is defined but never used.", "'setClientData' is assigned a value but never used.", "'useRef' is defined but never used.", "'Legend' is defined but never used.", "'useState' is defined but never used.", "'Code' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", {"desc": "261", "fix": "262"}, "Update the dependencies array to be: [activeCategory, location]", {"range": "263", "text": "264"}, [9369, 9379], "[activeCategory, location]"]