[{"D:\\Via\\New folder\\v2\\src\\index.js": "1", "D:\\Via\\New folder\\v2\\src\\App.js": "2", "D:\\Via\\New folder\\v2\\src\\reportWebVitals.js": "3", "D:\\Via\\New folder\\v2\\src\\pages\\Login.jsx": "4", "D:\\Via\\New folder\\v2\\src\\pages\\Watches.jsx": "5", "D:\\Via\\New folder\\v2\\src\\pages\\Bracelets.jsx": "6", "D:\\Via\\New folder\\v2\\src\\pages\\HowItWorks.jsx": "7", "D:\\Via\\New folder\\v2\\src\\pages\\Home.jsx": "8", "D:\\Via\\New folder\\v2\\src\\pages\\VirtualTryOn.jsx": "9", "D:\\Via\\New folder\\v2\\src\\pages\\WhyViaTryon.jsx": "10", "D:\\Via\\New folder\\v2\\src\\pages\\SearchResults.jsx": "11", "D:\\Via\\New folder\\v2\\src\\pages\\ProductDetails.jsx": "12", "D:\\Via\\New folder\\v2\\src\\pages\\Contact.jsx": "13", "D:\\Via\\New folder\\v2\\src\\pages\\Requirements.jsx": "14", "D:\\Via\\New folder\\v2\\src\\components\\DemoForm.jsx": "15", "D:\\Via\\New folder\\v2\\src\\components\\Footer.jsx": "16", "D:\\Via\\New folder\\v2\\src\\components\\Navbar.jsx": "17", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\Clients.jsx": "18", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\TryOnAnalytics.jsx": "19", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\AdminDashboard.jsx": "20", "D:\\Via\\New folder\\v2\\src\\pages\\admin\\Settings.jsx": "21", "D:\\Via\\New folder\\v2\\src\\pages\\client\\ClientDashboard.jsx": "22", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ClientAnalytics.jsx": "23", "D:\\Via\\New folder\\v2\\src\\utils\\imageLoader.js": "24", "D:\\Via\\New folder\\v2\\src\\services\\api.js": "25", "D:\\Via\\New folder\\v2\\src\\data\\productCollections.js": "26", "D:\\Via\\New folder\\v2\\src\\context\\CartContext.js": "27", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\UserEngagement.jsx": "28", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\TimeAnalysis.jsx": "29", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\Overview.jsx": "30", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ConversionRates.jsx": "31", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ProductPerformance.jsx": "32", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\DeviceStats.jsx": "33", "D:\\Via\\New folder\\v2\\src\\components\\admin\\AdminNavbar.jsx": "34", "D:\\Via\\New folder\\v2\\src\\components\\admin\\AdminSidebar.jsx": "35", "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\GeographicData.jsx": "36", "D:\\Via\\New folder\\v2\\src\\components\\client\\ClientSidebar.jsx": "37", "D:\\Via\\New folder\\v2\\src\\components\\client\\ClientNavbar.jsx": "38", "D:\\Via\\New folder\\v2\\src\\utils\\backgroundRemover.js": "39"}, {"size": 653, "mtime": 1749196504388, "results": "40", "hashOfConfig": "41"}, {"size": 4421, "mtime": 1749155740246, "results": "42", "hashOfConfig": "41"}, {"size": 362, "mtime": 1746042995461, "results": "43", "hashOfConfig": "41"}, {"size": 7675, "mtime": 1749196504239, "results": "44", "hashOfConfig": "41"}, {"size": 19373, "mtime": 1748874413890, "results": "45", "hashOfConfig": "41"}, {"size": 19487, "mtime": 1748874429932, "results": "46", "hashOfConfig": "41"}, {"size": 17189, "mtime": 1748864169684, "results": "47", "hashOfConfig": "41"}, {"size": 41162, "mtime": 1748975550119, "results": "48", "hashOfConfig": "41"}, {"size": 60392, "mtime": 1748605437244, "results": "49", "hashOfConfig": "41"}, {"size": 16901, "mtime": 1748864143496, "results": "50", "hashOfConfig": "41"}, {"size": 6108, "mtime": 1746344994148, "results": "51", "hashOfConfig": "41"}, {"size": 16294, "mtime": 1748293996467, "results": "52", "hashOfConfig": "41"}, {"size": 25359, "mtime": 1748867397685, "results": "53", "hashOfConfig": "41"}, {"size": 7100, "mtime": 1748867056681, "results": "54", "hashOfConfig": "41"}, {"size": 11616, "mtime": 1746342271795, "results": "55", "hashOfConfig": "41"}, {"size": 6479, "mtime": 1748866960444, "results": "56", "hashOfConfig": "41"}, {"size": 14287, "mtime": 1749152911428, "results": "57", "hashOfConfig": "41"}, {"size": 12614, "mtime": 1749144133083, "results": "58", "hashOfConfig": "41"}, {"size": 12445, "mtime": 1749144139761, "results": "59", "hashOfConfig": "41"}, {"size": 8729, "mtime": 1749144128361, "results": "60", "hashOfConfig": "41"}, {"size": 12062, "mtime": 1749144136782, "results": "61", "hashOfConfig": "41"}, {"size": 4213, "mtime": 1749152998041, "results": "62", "hashOfConfig": "41"}, {"size": 3734, "mtime": 1749198331118, "results": "63", "hashOfConfig": "41"}, {"size": 8174, "mtime": 1748283061371, "results": "64", "hashOfConfig": "41"}, {"size": 1190, "mtime": 1748291506520, "results": "65", "hashOfConfig": "41"}, {"size": 10601, "mtime": 1748277235110, "results": "66", "hashOfConfig": "41"}, {"size": 4297, "mtime": 1748283089634, "results": "67", "hashOfConfig": "41"}, {"size": 6733, "mtime": 1749196502414, "results": "68", "hashOfConfig": "41"}, {"size": 6636, "mtime": 1749198338414, "results": "69", "hashOfConfig": "41"}, {"size": 8376, "mtime": 1749196500851, "results": "70", "hashOfConfig": "41"}, {"size": 5620, "mtime": 1749196502795, "results": "71", "hashOfConfig": "41"}, {"size": 6238, "mtime": 1749196501461, "results": "72", "hashOfConfig": "41"}, {"size": 6156, "mtime": 1749196502910, "results": "73", "hashOfConfig": "41"}, {"size": 8877, "mtime": 1749150209342, "results": "74", "hashOfConfig": "41"}, {"size": 7144, "mtime": 1749144357974, "results": "75", "hashOfConfig": "41"}, {"size": 5658, "mtime": 1749196503295, "results": "76", "hashOfConfig": "41"}, {"size": 8337, "mtime": 1749196503594, "results": "77", "hashOfConfig": "41"}, {"size": 10834, "mtime": 1749196503552, "results": "78", "hashOfConfig": "41"}, {"size": 5096, "mtime": 1748291546769, "results": "79", "hashOfConfig": "41"}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gdfhta", {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Via\\New folder\\v2\\src\\index.js", [], [], "D:\\Via\\New folder\\v2\\src\\App.js", ["197"], [], "D:\\Via\\New folder\\v2\\src\\reportWebVitals.js", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Login.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Watches.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Bracelets.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\HowItWorks.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Home.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\VirtualTryOn.jsx", ["198"], [], "D:\\Via\\New folder\\v2\\src\\pages\\WhyViaTryon.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\SearchResults.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\ProductDetails.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Contact.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\Requirements.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\DemoForm.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\Footer.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\Navbar.jsx", ["199"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\Clients.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\TryOnAnalytics.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\AdminDashboard.jsx", ["200"], [], "D:\\Via\\New folder\\v2\\src\\pages\\admin\\Settings.jsx", ["201", "202"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\ClientDashboard.jsx", ["203"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ClientAnalytics.jsx", ["204", "205"], [], "D:\\Via\\New folder\\v2\\src\\utils\\imageLoader.js", [], [], "D:\\Via\\New folder\\v2\\src\\services\\api.js", [], [], "D:\\Via\\New folder\\v2\\src\\data\\productCollections.js", [], [], "D:\\Via\\New folder\\v2\\src\\context\\CartContext.js", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\UserEngagement.jsx", ["206", "207", "208"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\TimeAnalysis.jsx", ["209"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\Overview.jsx", ["210"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ConversionRates.jsx", ["211"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\ProductPerformance.jsx", ["212"], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\DeviceStats.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\admin\\AdminNavbar.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\components\\admin\\AdminSidebar.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\pages\\client\\analytics\\GeographicData.jsx", ["213"], [], "D:\\Via\\New folder\\v2\\src\\components\\client\\ClientSidebar.jsx", ["214"], [], "D:\\Via\\New folder\\v2\\src\\components\\client\\ClientNavbar.jsx", [], [], "D:\\Via\\New folder\\v2\\src\\utils\\backgroundRemover.js", [], [], {"ruleId": "215", "severity": 1, "message": "216", "line": 40, "column": 9, "nodeType": "217", "messageId": "218", "endLine": 40, "endColumn": 13}, {"ruleId": "219", "severity": 1, "message": "220", "line": 203, "column": 6, "nodeType": "221", "endLine": 203, "endColumn": 16, "suggestions": "222"}, {"ruleId": "215", "severity": 1, "message": "223", "line": 37, "column": 9, "nodeType": "217", "messageId": "218", "endLine": 37, "endColumn": 17}, {"ruleId": "215", "severity": 1, "message": "224", "line": 4, "column": 10, "nodeType": "217", "messageId": "218", "endLine": 4, "endColumn": 16}, {"ruleId": "215", "severity": 1, "message": "224", "line": 4, "column": 10, "nodeType": "217", "messageId": "218", "endLine": 4, "endColumn": 16}, {"ruleId": "215", "severity": 1, "message": "225", "line": 26, "column": 9, "nodeType": "217", "messageId": "218", "endLine": 26, "endColumn": 26}, {"ruleId": "215", "severity": 1, "message": "224", "line": 4, "column": 10, "nodeType": "217", "messageId": "218", "endLine": 4, "endColumn": 16}, {"ruleId": "215", "severity": 1, "message": "226", "line": 1, "column": 27, "nodeType": "217", "messageId": "218", "endLine": 1, "endColumn": 36}, {"ruleId": "215", "severity": 1, "message": "227", "line": 1, "column": 38, "nodeType": "217", "messageId": "218", "endLine": 1, "endColumn": 44}, {"ruleId": "215", "severity": 1, "message": "228", "line": 5, "column": 3, "nodeType": "217", "messageId": "218", "endLine": 5, "endColumn": 11}, {"ruleId": "215", "severity": 1, "message": "229", "line": 6, "column": 3, "nodeType": "217", "messageId": "218", "endLine": 6, "endColumn": 6}, {"ruleId": "215", "severity": 1, "message": "230", "line": 14, "column": 3, "nodeType": "217", "messageId": "218", "endLine": 14, "endColumn": 9}, {"ruleId": "215", "severity": 1, "message": "230", "line": 11, "column": 3, "nodeType": "217", "messageId": "218", "endLine": 11, "endColumn": 9}, {"ruleId": "215", "severity": 1, "message": "230", "line": 14, "column": 3, "nodeType": "217", "messageId": "218", "endLine": 14, "endColumn": 9}, {"ruleId": "215", "severity": 1, "message": "230", "line": 11, "column": 3, "nodeType": "217", "messageId": "218", "endLine": 11, "endColumn": 9}, {"ruleId": "215", "severity": 1, "message": "230", "line": 11, "column": 3, "nodeType": "217", "messageId": "218", "endLine": 11, "endColumn": 9}, {"ruleId": "215", "severity": 1, "message": "230", "line": 9, "column": 3, "nodeType": "217", "messageId": "218", "endLine": 9, "endColumn": 9}, {"ruleId": "215", "severity": 1, "message": "231", "line": 1, "column": 17, "nodeType": "217", "messageId": "218", "endLine": 1, "endColumn": 25}, "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'activeCategory'. Either include it or remove the dependency array.", "ArrayExpression", ["232"], "'isClient' is assigned a value but never used.", "'motion' is defined but never used.", "'handleInputChange' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useRef' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Legend' is defined but never used.", "'useState' is defined but never used.", {"desc": "233", "fix": "234"}, "Update the dependencies array to be: [activeCategory, location]", {"range": "235", "text": "236"}, [9369, 9379], "[activeCategory, location]"]