import React, { useState, useEffect } from 'react';
import Client<PERSON>idebar from '../../components/client/ClientSidebar';
import ClientNavbar from '../../components/client/ClientNavbar';
import EmbedCodeGenerator from '../../components/EmbedCodeGenerator';
import { motion } from 'framer-motion';
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { Eye, TrendingUp, Users, ShoppingCart, Clock, Code, Globe, Smartphone } from 'lucide-react';

const ClientDashboard = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [timeRange, setTimeRange] = useState('7d');
  const [showEmbedModal, setShowEmbedModal] = useState(false);
  const [clientData, setClientData] = useState({
    id: 'luxury-watches-co',
    totalTryOns: 2840,
    conversionRate: 22.4,
    avgDuration: 165, // seconds
    uniqueUsers: 1250,
    revenue: 45600
  });

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Calculate margin for main content
  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';

  // Sample data for charts
  const tryOnTrends = [
    { date: '2024-03-01', tryOns: 180, conversions: 42 },
    { date: '2024-03-02', tryOns: 220, conversions: 48 },
    { date: '2024-03-03', tryOns: 195, conversions: 44 },
    { date: '2024-03-04', tryOns: 280, conversions: 65 },
    { date: '2024-03-05', tryOns: 320, conversions: 72 },
    { date: '2024-03-06', tryOns: 290, conversions: 68 },
    { date: '2024-03-07', tryOns: 350, conversions: 78 },
  ];

  const productPerformance = [
    { name: 'Watch Model A', tryOns: 450, conversions: 98 },
    { name: 'Watch Model B', tryOns: 380, conversions: 84 },
    { name: 'Watch Model C', tryOns: 290, conversions: 65 },
    { name: 'Bracelet A', tryOns: 210, conversions: 47 },
    { name: 'Bracelet B', tryOns: 180, conversions: 40 },
  ];

  const deviceStats = [
    { name: 'Mobile', value: 72, color: '#2D8C88' },
    { name: 'Desktop', value: 22, color: '#3B82F6' },
    { name: 'Tablet', value: 6, color: '#10B981' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <ClientSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />
      <ClientNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />

      {/* Main Content */}
      <main className={`${mainMargin} pt-16 transition-all duration-300`}>
        <div className="p-4 md:p-6">
          {/* Page Header */}
          <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Virtual Try-On Dashboard</h1>
              <p className="text-gray-600">Monitor your product performance and customer engagement</p>
            </div>
            <div className="mt-4 md:mt-0 flex space-x-3">
              <div className="inline-flex rounded-lg border border-gray-200 p-1">
                {['7d', '30d', '90d', '1y'].map((range) => (
                  <button
                    key={range}
                    onClick={() => setTimeRange(range)}
                    className={`px-3 py-1 text-sm font-medium rounded-md ${
                      timeRange === range
                        ? 'bg-[#2D8C88] text-white'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    {range}
                  </button>
                ))}
              </div>
              <button
                onClick={() => setShowEmbedModal(true)}
                className="inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2"
              >
                <Code className="h-4 w-4 mr-2" />
                Get Embed Code
              </button>
            </div>
          </div>

          {/* Enhanced Stats Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 md:gap-6 mb-6">
            {/* Total Try-Ons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Try-Ons</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{clientData.totalTryOns.toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
                  <Eye className="h-6 w-6 text-[#2D8C88]" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm font-medium text-green-600">+18%</span>
                <span className="text-sm text-gray-600 ml-2">from last week</span>
              </div>
            </motion.div>

            {/* Conversion Rate */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{clientData.conversionRate}%</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-green-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm font-medium text-green-600">+2.4%</span>
                <span className="text-sm text-gray-600 ml-2">from last month</span>
              </div>
            </motion.div>

            {/* Average Duration */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Duration</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{Math.floor(clientData.avgDuration / 60)}m {clientData.avgDuration % 60}s</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                  <Clock className="h-6 w-6 text-blue-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm font-medium text-green-600">+15s</span>
                <span className="text-sm text-gray-600 ml-2">from last month</span>
              </div>
            </motion.div>

            {/* Unique Users */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Unique Users</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{clientData.uniqueUsers.toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center">
                  <Users className="h-6 w-6 text-purple-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm font-medium text-green-600">+12%</span>
                <span className="text-sm text-gray-600 ml-2">from last week</span>
              </div>
            </motion.div>

            {/* Revenue Impact */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Revenue Impact</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">${clientData.revenue.toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-orange-500/10 flex items-center justify-center">
                  <ShoppingCart className="h-6 w-6 text-orange-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm font-medium text-green-600">+28%</span>
                <span className="text-sm text-gray-600 ml-2">from last month</span>
              </div>
            </motion.div>
          </div>

          {/* Charts Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Try-On Trends */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <h3 className="text-lg font-medium text-gray-900 mb-4">Try-On Trends</h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={tryOnTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Line
                      type="monotone"
                      dataKey="tryOns"
                      stroke="#2D8C88"
                      strokeWidth={2}
                      dot={{ fill: '#2D8C88' }}
                      name="Try-Ons"
                    />
                    <Line
                      type="monotone"
                      dataKey="conversions"
                      stroke="#10B981"
                      strokeWidth={2}
                      dot={{ fill: '#10B981' }}
                      name="Conversions"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </motion.div>

            {/* Device Distribution */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <h3 className="text-lg font-medium text-gray-900 mb-4">Device Usage</h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={deviceStats}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {deviceStats.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </motion.div>
          </div>

          {/* Product Performance */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="bg-white rounded-xl shadow-sm p-6 mb-6"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">Top Performing Products</h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={productPerformance}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="tryOns" fill="#2D8C88" name="Try-Ons" />
                  <Bar dataKey="conversions" fill="#10B981" name="Conversions" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </motion.div>

          {/* Integration Guide */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="bg-gradient-to-r from-[#2D8C88] to-[#236b68] rounded-xl shadow-sm p-6 mb-6"
          >
            <div className="flex items-center justify-between text-white">
              <div>
                <h3 className="text-lg font-medium">Ready to integrate Virtual Try-On?</h3>
                <p className="text-[#2D8C88]/80 mt-1">Add our try-on button to your product pages in minutes</p>
              </div>
              <div className="flex space-x-3">
                <button className="bg-white text-[#2D8C88] px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                  View Guide
                </button>
                <button className="bg-[#236b68] text-white px-4 py-2 rounded-lg font-medium hover:bg-[#1e5a57] transition-colors">
                  Get Code
                </button>
              </div>
            </div>
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9 }}
            className="bg-white rounded-xl shadow-sm overflow-hidden"
          >
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
            </div>
            <div className="divide-y divide-gray-200">
              {/* Activity Items */}
              <div className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
                    <Eye className="h-6 w-6 text-[#2D8C88]" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">High engagement detected</p>
                    <p className="text-sm text-gray-500">Watch Model A had 50+ try-ons in the last hour</p>
                  </div>
                  <div className="ml-auto">
                    <p className="text-sm text-gray-500">1 hour ago</p>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 rounded-full bg-green-500/10 flex items-center justify-center">
                    <TrendingUp className="h-6 w-6 text-green-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Conversion milestone reached</p>
                    <p className="text-sm text-gray-500">Your conversion rate exceeded 22% this week</p>
                  </div>
                  <div className="ml-auto">
                    <p className="text-sm text-gray-500">3 hours ago</p>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 rounded-full bg-blue-500/10 flex items-center justify-center">
                    <Smartphone className="h-6 w-6 text-blue-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Mobile usage spike</p>
                    <p className="text-sm text-gray-500">72% of try-ons came from mobile devices today</p>
                  </div>
                  <div className="ml-auto">
                    <p className="text-sm text-gray-500">5 hours ago</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </main>

      {/* Embed Code Modal */}
      <EmbedCodeGenerator
        isOpen={showEmbedModal}
        onClose={() => setShowEmbedModal(false)}
        clientData={clientData}
      />
    </div>
  );
};

export default ClientDashboard; 