import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';

const ProductPerformance = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('tryOns');

  // Sample data
  const productData = [
    { name: 'Watch Model A', tryOns: 450, views: 1200, conversions: 45, avgDuration: 180 },
    { name: 'Watch Model B', tryOns: 380, views: 980, conversions: 38, avgDuration: 165 },
    { name: 'Watch Model C', tryOns: 290, views: 850, conversions: 29, avgDuration: 150 },
    { name: 'Watch Model D', tryOns: 210, views: 720, conversions: 21, avgDuration: 140 },
    { name: 'Watch Model E', tryOns: 180, views: 650, conversions: 18, avgDuration: 135 },
  ];

  const trendData = [
    { date: '2024-03-01', tryOns: 120, views: 320, conversions: 12 },
    { date: '2024-03-02', tryOns: 150, views: 380, conversions: 15 },
    { date: '2024-03-03', tryOns: 180, views: 420, conversions: 18 },
    { date: '2024-03-04', tryOns: 160, views: 400, conversions: 16 },
    { date: '2024-03-05', tryOns: 200, views: 450, conversions: 20 },
    { date: '2024-03-06', tryOns: 220, views: 480, conversions: 22 },
    { date: '2024-03-07', tryOns: 250, views: 520, conversions: 25 },
  ];

  const metrics = [
    { id: 'tryOns', label: 'Try-Ons' },
    { id: 'views', label: 'Views' },
    { id: 'conversions', label: 'Conversions' },
    { id: 'avgDuration', label: 'Avg. Duration' },
  ];

  return (
    <div className="p-6">
      {/* Time Range and Metric Selector */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 space-y-4 md:space-y-0">
        <div className="inline-flex rounded-lg border border-gray-200 p-1">
          {['7d', '30d', '90d', '1y'].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                timeRange === range
                  ? 'bg-[#2D8C88] text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {range}
            </button>
          ))}
        </div>
        <div className="inline-flex rounded-lg border border-gray-200 p-1">
          {metrics.map((metric) => (
            <button
              key={metric.id}
              onClick={() => setSelectedMetric(metric.id)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                selectedMetric === metric.id
                  ? 'bg-[#2D8C88] text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {metric.label}
            </button>
          ))}
        </div>
      </div>

      {/* Product Performance Chart */}
      <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Product Performance</h3>
        <div className="h-96">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={productData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey={selectedMetric} fill="#2D8C88" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Trend Analysis */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Trend Analysis</h3>
        <div className="h-96">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Line
                type="monotone"
                dataKey={selectedMetric}
                stroke="#2D8C88"
                strokeWidth={2}
                dot={{ fill: '#2D8C88' }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Product Details Table */}
      <div className="mt-6 bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Product Details</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Try-Ons</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Views</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conversions</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Duration</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {productData.map((product) => (
                <tr key={product.name} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{product.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.tryOns}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.views}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.conversions}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.avgDuration}s</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ProductPerformance; 