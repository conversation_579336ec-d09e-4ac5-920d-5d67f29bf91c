{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\pages\\\\client\\\\analytics\\\\Overview.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { LineChart, Line, BarChart, Bar, Pie<PERSON>hart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Overview = () => {\n  _s();\n  const [timeRange, setTimeRange] = useState('7d');\n\n  // Sample data for charts\n  const tryOnData = [{\n    date: '2024-03-01',\n    count: 120\n  }, {\n    date: '2024-03-02',\n    count: 150\n  }, {\n    date: '2024-03-03',\n    count: 180\n  }, {\n    date: '2024-03-04',\n    count: 160\n  }, {\n    date: '2024-03-05',\n    count: 200\n  }, {\n    date: '2024-03-06',\n    count: 220\n  }, {\n    date: '2024-03-07',\n    count: 250\n  }];\n  const productData = [{\n    name: 'Product A',\n    value: 400\n  }, {\n    name: 'Product B',\n    value: 300\n  }, {\n    name: 'Product C',\n    value: 300\n  }, {\n    name: 'Product D',\n    value: 200\n  }];\n  const deviceData = [{\n    name: 'Mobile',\n    value: 60\n  }, {\n    name: 'Desktop',\n    value: 30\n  }, {\n    name: 'Tablet',\n    value: 10\n  }];\n  const COLORS = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B'];\n  const metrics = [{\n    title: 'Total Try-Ons',\n    value: '1,280',\n    change: '+15%',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Average Duration',\n    value: '2m 45s',\n    change: '+8%',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Conversion Rate',\n    value: '24.8%',\n    change: '+4%',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Unique Users',\n    value: '856',\n    change: '+12%',\n    trend: 'up',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-end mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-flex rounded-lg border border-gray-200 p-1\",\n        children: ['7d', '30d', '90d', '1y'].map(range => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setTimeRange(range),\n          className: `px-3 py-1 text-sm font-medium rounded-md ${timeRange === range ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:text-gray-900'}`,\n          children: range\n        }, range, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n      children: metrics.map(metric => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: metric.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-semibold text-gray-900 mt-1\",\n              children: metric.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n            children: metric.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-sm font-medium ${metric.trend === 'up' ? 'text-green-600' : 'text-red-600'}`,\n            children: metric.change\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600 ml-2\",\n            children: \"from last period\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 13\n        }, this)]\n      }, metric.title, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Try-On Trends\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-80\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: tryOnData,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"count\",\n                stroke: \"#2D8C88\",\n                strokeWidth: 2,\n                dot: {\n                  fill: '#2D8C88'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Top Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-80\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: productData,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"value\",\n                fill: \"#2D8C88\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Device Distribution\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-80\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: deviceData,\n                cx: \"50%\",\n                cy: \"50%\",\n                labelLine: false,\n                outerRadius: 80,\n                fill: \"#8884d8\",\n                dataKey: \"value\",\n                label: ({\n                  name,\n                  percent\n                }) => `${name} ${(percent * 100).toFixed(0)}%`,\n                children: deviceData.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: COLORS[index % COLORS.length]\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Recent Activity\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [1, 2, 3, 4, 5].map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6 text-[#2D8C88]\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: \"Try-on session completed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Product: Watch Model X\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-auto\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"2 hours ago\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this)]\n          }, item, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s(Overview, \"S/Xk/B2yitnwtU8ogBi58+n1JKE=\");\n_c = Overview;\nexport default Overview;\nvar _c;\n$RefreshReg$(_c, \"Overview\");", "map": {"version": 3, "names": ["React", "useState", "Line<PERSON>hart", "Line", "<PERSON><PERSON><PERSON>", "Bar", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "jsxDEV", "_jsxDEV", "Overview", "_s", "timeRange", "setTimeRange", "tryOnData", "date", "count", "productData", "name", "value", "deviceData", "COLORS", "metrics", "title", "change", "trend", "icon", "xmlns", "className", "fill", "viewBox", "stroke", "children", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "range", "onClick", "metric", "width", "height", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "type", "dot", "cx", "cy", "labelLine", "outerRadius", "label", "percent", "toFixed", "entry", "index", "length", "item", "_c", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/pages/client/analytics/Overview.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON><PERSON>hart,\n  Line,\n  <PERSON><PERSON>hart,\n  Bar,\n  Pie<PERSON>hart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  <PERSON><PERSON><PERSON>,\n  Legend,\n  ResponsiveContainer\n} from 'recharts';\n\nconst Overview = () => {\n  const [timeRange, setTimeRange] = useState('7d');\n\n  // Sample data for charts\n  const tryOnData = [\n    { date: '2024-03-01', count: 120 },\n    { date: '2024-03-02', count: 150 },\n    { date: '2024-03-03', count: 180 },\n    { date: '2024-03-04', count: 160 },\n    { date: '2024-03-05', count: 200 },\n    { date: '2024-03-06', count: 220 },\n    { date: '2024-03-07', count: 250 },\n  ];\n\n  const productData = [\n    { name: 'Product A', value: 400 },\n    { name: 'Product B', value: 300 },\n    { name: 'Product C', value: 300 },\n    { name: 'Product D', value: 200 },\n  ];\n\n  const deviceData = [\n    { name: 'Mobile', value: 60 },\n    { name: 'Desktop', value: 30 },\n    { name: 'Tablet', value: 10 },\n  ];\n\n  const COLORS = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B'];\n\n  const metrics = [\n    {\n      title: 'Total Try-Ons',\n      value: '1,280',\n      change: '+15%',\n      trend: 'up',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n        </svg>\n      ),\n    },\n    {\n      title: 'Average Duration',\n      value: '2m 45s',\n      change: '+8%',\n      trend: 'up',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n        </svg>\n      ),\n    },\n    {\n      title: 'Conversion Rate',\n      value: '24.8%',\n      change: '+4%',\n      trend: 'up',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n        </svg>\n      ),\n    },\n    {\n      title: 'Unique Users',\n      value: '856',\n      change: '+12%',\n      trend: 'up',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n        </svg>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"p-6\">\n      {/* Time Range Selector */}\n      <div className=\"flex justify-end mb-6\">\n        <div className=\"inline-flex rounded-lg border border-gray-200 p-1\">\n          {['7d', '30d', '90d', '1y'].map((range) => (\n            <button\n              key={range}\n              onClick={() => setTimeRange(range)}\n              className={`px-3 py-1 text-sm font-medium rounded-md ${\n                timeRange === range\n                  ? 'bg-[#2D8C88] text-white'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              {range}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n        {metrics.map((metric) => (\n          <div key={metric.title} className=\"bg-white rounded-xl shadow-sm p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">{metric.title}</p>\n                <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{metric.value}</p>\n              </div>\n              <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\n                {metric.icon}\n              </div>\n            </div>\n            <div className=\"mt-4\">\n              <span className={`text-sm font-medium ${\n                metric.trend === 'up' ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {metric.change}\n              </span>\n              <span className=\"text-sm text-gray-600 ml-2\">from last period</span>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Charts Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Try-On Trends */}\n        <div className=\"bg-white rounded-xl shadow-sm p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Try-On Trends</h3>\n          <div className=\"h-80\">\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <LineChart data={tryOnData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"date\" />\n                <YAxis />\n                <Tooltip />\n                <Line\n                  type=\"monotone\"\n                  dataKey=\"count\"\n                  stroke=\"#2D8C88\"\n                  strokeWidth={2}\n                  dot={{ fill: '#2D8C88' }}\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n\n        {/* Top Products */}\n        <div className=\"bg-white rounded-xl shadow-sm p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Top Products</h3>\n          <div className=\"h-80\">\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <BarChart data={productData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"name\" />\n                <YAxis />\n                <Tooltip />\n                <Bar dataKey=\"value\" fill=\"#2D8C88\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n\n        {/* Device Distribution */}\n        <div className=\"bg-white rounded-xl shadow-sm p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Device Distribution</h3>\n          <div className=\"h-80\">\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <PieChart>\n                <Pie\n                  data={deviceData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"value\"\n                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n                >\n                  {deviceData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n                  ))}\n                </Pie>\n                <Tooltip />\n              </PieChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n\n        {/* Recent Activity */}\n        <div className=\"bg-white rounded-xl shadow-sm p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Recent Activity</h3>\n          <div className=\"space-y-4\">\n            {[1, 2, 3, 4, 5].map((item) => (\n              <div key={item} className=\"flex items-center space-x-4\">\n                <div className=\"w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-[#2D8C88]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-sm font-medium text-gray-900\">Try-on session completed</p>\n                  <p className=\"text-sm text-gray-500\">Product: Watch Model X</p>\n                </div>\n                <div className=\"ml-auto\">\n                  <p className=\"text-sm text-gray-500\">2 hours ago</p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Overview; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,GAAG,EACHC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,QACd,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAMoB,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAI,CAAC,EAClC;IAAED,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAI,CAAC,EAClC;IAAED,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAI,CAAC,EAClC;IAAED,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAI,CAAC,EAClC;IAAED,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAI,CAAC,EAClC;IAAED,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAI,CAAC,EAClC;IAAED,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAI,CAAC,CACnC;EAED,MAAMC,WAAW,GAAG,CAClB;IAAEC,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAI,CAAC,EACjC;IAAED,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAI,CAAC,EACjC;IAAED,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAI,CAAC,EACjC;IAAED,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAI,CAAC,CAClC;EAED,MAAMC,UAAU,GAAG,CACjB;IAAEF,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAG,CAAC,EAC7B;IAAED,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAG,CAAC,EAC9B;IAAED,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAG,CAAC,CAC9B;EAED,MAAME,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAE3D,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,eAAe;IACtBJ,KAAK,EAAE,OAAO;IACdK,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,IAAI;IACXC,IAAI,eACFjB,OAAA;MAAKkB,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/GvB,OAAA;QAAMwB,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAoI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzM;EAET,CAAC,EACD;IACEjB,KAAK,EAAE,kBAAkB;IACzBJ,KAAK,EAAE,QAAQ;IACfK,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE,IAAI;IACXC,IAAI,eACFjB,OAAA;MAAKkB,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/GvB,OAAA;QAAMwB,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAA6C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClH;EAET,CAAC,EACD;IACEjB,KAAK,EAAE,iBAAiB;IACxBJ,KAAK,EAAE,OAAO;IACdK,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE,IAAI;IACXC,IAAI,eACFjB,OAAA;MAAKkB,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/GvB,OAAA;QAAMwB,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrG;EAET,CAAC,EACD;IACEjB,KAAK,EAAE,cAAc;IACrBJ,KAAK,EAAE,KAAK;IACZK,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,IAAI;IACXC,IAAI,eACFjB,OAAA;MAAKkB,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/GvB,OAAA;QAAMwB,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAwQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7U;EAET,CAAC,CACF;EAED,oBACE/B,OAAA;IAAKmB,SAAS,EAAC,KAAK;IAAAI,QAAA,gBAElBvB,OAAA;MAAKmB,SAAS,EAAC,uBAAuB;MAAAI,QAAA,eACpCvB,OAAA;QAAKmB,SAAS,EAAC,mDAAmD;QAAAI,QAAA,EAC/D,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAACS,GAAG,CAAEC,KAAK,iBACpCjC,OAAA;UAEEkC,OAAO,EAAEA,CAAA,KAAM9B,YAAY,CAAC6B,KAAK,CAAE;UACnCd,SAAS,EAAE,4CACThB,SAAS,KAAK8B,KAAK,GACf,yBAAyB,GACzB,mCAAmC,EACtC;UAAAV,QAAA,EAEFU;QAAK,GARDA,KAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASJ,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/B,OAAA;MAAKmB,SAAS,EAAC,2DAA2D;MAAAI,QAAA,EACvEV,OAAO,CAACmB,GAAG,CAAEG,MAAM,iBAClBnC,OAAA;QAAwBmB,SAAS,EAAC,mCAAmC;QAAAI,QAAA,gBACnEvB,OAAA;UAAKmB,SAAS,EAAC,mCAAmC;UAAAI,QAAA,gBAChDvB,OAAA;YAAAuB,QAAA,gBACEvB,OAAA;cAAGmB,SAAS,EAAC,mCAAmC;cAAAI,QAAA,EAAEY,MAAM,CAACrB;YAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnE/B,OAAA;cAAGmB,SAAS,EAAC,2CAA2C;cAAAI,QAAA,EAAEY,MAAM,CAACzB;YAAK;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eACN/B,OAAA;YAAKmB,SAAS,EAAC,yEAAyE;YAAAI,QAAA,EACrFY,MAAM,CAAClB;UAAI;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/B,OAAA;UAAKmB,SAAS,EAAC,MAAM;UAAAI,QAAA,gBACnBvB,OAAA;YAAMmB,SAAS,EAAE,uBACfgB,MAAM,CAACnB,KAAK,KAAK,IAAI,GAAG,gBAAgB,GAAG,cAAc,EACxD;YAAAO,QAAA,EACAY,MAAM,CAACpB;UAAM;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACP/B,OAAA;YAAMmB,SAAS,EAAC,4BAA4B;YAAAI,QAAA,EAAC;UAAgB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA,GAjBEI,MAAM,CAACrB,KAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkBjB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN/B,OAAA;MAAKmB,SAAS,EAAC,uCAAuC;MAAAI,QAAA,gBAEpDvB,OAAA;QAAKmB,SAAS,EAAC,mCAAmC;QAAAI,QAAA,gBAChDvB,OAAA;UAAImB,SAAS,EAAC,wCAAwC;UAAAI,QAAA,EAAC;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzE/B,OAAA;UAAKmB,SAAS,EAAC,MAAM;UAAAI,QAAA,eACnBvB,OAAA,CAACF,mBAAmB;YAACsC,KAAK,EAAC,MAAM;YAACC,MAAM,EAAC,MAAM;YAAAd,QAAA,eAC7CvB,OAAA,CAACd,SAAS;cAACoD,IAAI,EAAEjC,SAAU;cAAAkB,QAAA,gBACzBvB,OAAA,CAACL,aAAa;gBAAC4C,eAAe,EAAC;cAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC/B,OAAA,CAACP,KAAK;gBAAC+C,OAAO,EAAC;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxB/B,OAAA,CAACN,KAAK;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT/B,OAAA,CAACJ,OAAO;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX/B,OAAA,CAACb,IAAI;gBACHsD,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,OAAO;gBACflB,MAAM,EAAC,SAAS;gBAChBI,WAAW,EAAE,CAAE;gBACfgB,GAAG,EAAE;kBAAEtB,IAAI,EAAE;gBAAU;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/B,OAAA;QAAKmB,SAAS,EAAC,mCAAmC;QAAAI,QAAA,gBAChDvB,OAAA;UAAImB,SAAS,EAAC,wCAAwC;UAAAI,QAAA,EAAC;QAAY;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxE/B,OAAA;UAAKmB,SAAS,EAAC,MAAM;UAAAI,QAAA,eACnBvB,OAAA,CAACF,mBAAmB;YAACsC,KAAK,EAAC,MAAM;YAACC,MAAM,EAAC,MAAM;YAAAd,QAAA,eAC7CvB,OAAA,CAACZ,QAAQ;cAACkD,IAAI,EAAE9B,WAAY;cAAAe,QAAA,gBAC1BvB,OAAA,CAACL,aAAa;gBAAC4C,eAAe,EAAC;cAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC/B,OAAA,CAACP,KAAK;gBAAC+C,OAAO,EAAC;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxB/B,OAAA,CAACN,KAAK;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT/B,OAAA,CAACJ,OAAO;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX/B,OAAA,CAACX,GAAG;gBAACmD,OAAO,EAAC,OAAO;gBAACpB,IAAI,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/B,OAAA;QAAKmB,SAAS,EAAC,mCAAmC;QAAAI,QAAA,gBAChDvB,OAAA;UAAImB,SAAS,EAAC,wCAAwC;UAAAI,QAAA,EAAC;QAAmB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/E/B,OAAA;UAAKmB,SAAS,EAAC,MAAM;UAAAI,QAAA,eACnBvB,OAAA,CAACF,mBAAmB;YAACsC,KAAK,EAAC,MAAM;YAACC,MAAM,EAAC,MAAM;YAAAd,QAAA,eAC7CvB,OAAA,CAACV,QAAQ;cAAAiC,QAAA,gBACPvB,OAAA,CAACT,GAAG;gBACF+C,IAAI,EAAE3B,UAAW;gBACjBgC,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACRC,SAAS,EAAE,KAAM;gBACjBC,WAAW,EAAE,EAAG;gBAChB1B,IAAI,EAAC,SAAS;gBACdoB,OAAO,EAAC,OAAO;gBACfO,KAAK,EAAEA,CAAC;kBAAEtC,IAAI;kBAAEuC;gBAAQ,CAAC,KAAK,GAAGvC,IAAI,IAAI,CAACuC,OAAO,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAI;gBAAA1B,QAAA,EAEtEZ,UAAU,CAACqB,GAAG,CAAC,CAACkB,KAAK,EAAEC,KAAK,kBAC3BnD,OAAA,CAACR,IAAI;kBAAuB4B,IAAI,EAAER,MAAM,CAACuC,KAAK,GAAGvC,MAAM,CAACwC,MAAM;gBAAE,GAArD,QAAQD,KAAK,EAAE;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAwC,CACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN/B,OAAA,CAACJ,OAAO;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/B,OAAA;QAAKmB,SAAS,EAAC,mCAAmC;QAAAI,QAAA,gBAChDvB,OAAA;UAAImB,SAAS,EAAC,wCAAwC;UAAAI,QAAA,EAAC;QAAe;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3E/B,OAAA;UAAKmB,SAAS,EAAC,WAAW;UAAAI,QAAA,EACvB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACS,GAAG,CAAEqB,IAAI,iBACxBrD,OAAA;YAAgBmB,SAAS,EAAC,6BAA6B;YAAAI,QAAA,gBACrDvB,OAAA;cAAKmB,SAAS,EAAC,yEAAyE;cAAAI,QAAA,eACtFvB,OAAA;gBAAKkB,KAAK,EAAC,4BAA4B;gBAACC,SAAS,EAAC,wBAAwB;gBAACC,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAC,QAAA,eAC9HvB,OAAA;kBAAMwB,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAqE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN/B,OAAA;cAAAuB,QAAA,gBACEvB,OAAA;gBAAGmB,SAAS,EAAC,mCAAmC;gBAAAI,QAAA,EAAC;cAAwB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC7E/B,OAAA;gBAAGmB,SAAS,EAAC,uBAAuB;gBAAAI,QAAA,EAAC;cAAsB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACN/B,OAAA;cAAKmB,SAAS,EAAC,SAAS;cAAAI,QAAA,eACtBvB,OAAA;gBAAGmB,SAAS,EAAC,uBAAuB;gBAAAI,QAAA,EAAC;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA,GAZEsB,IAAI;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaT,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7B,EAAA,CArNID,QAAQ;AAAAqD,EAAA,GAARrD,QAAQ;AAuNd,eAAeA,QAAQ;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}