{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\pages\\\\client\\\\analytics\\\\UserEngagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { <PERSON><PERSON>hart, Line, <PERSON><PERSON>hart, <PERSON>, <PERSON>hart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserEngagement = () => {\n  _s();\n  const [timeRange, setTimeRange] = useState('7d');\n\n  // Sample data\n  const engagementData = [{\n    date: '2024-03-01',\n    sessions: 120,\n    avgDuration: 180,\n    interactions: 450\n  }, {\n    date: '2024-03-02',\n    sessions: 150,\n    avgDuration: 185,\n    interactions: 480\n  }, {\n    date: '2024-03-03',\n    sessions: 180,\n    avgDuration: 190,\n    interactions: 520\n  }, {\n    date: '2024-03-04',\n    sessions: 160,\n    avgDuration: 175,\n    interactions: 460\n  }, {\n    date: '2024-03-05',\n    sessions: 200,\n    avgDuration: 195,\n    interactions: 580\n  }, {\n    date: '2024-03-06',\n    sessions: 220,\n    avgDuration: 200,\n    interactions: 620\n  }, {\n    date: '2024-03-07',\n    sessions: 250,\n    avgDuration: 205,\n    interactions: 680\n  }];\n  const interactionTypes = [{\n    name: 'Product Rotation',\n    value: 35\n  }, {\n    name: 'Color Change',\n    value: 25\n  }, {\n    name: 'Size Adjustment',\n    value: 20\n  }, {\n    name: 'Share',\n    value: 15\n  }, {\n    name: 'Other',\n    value: 5\n  }];\n  const COLORS = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'];\n  const metrics = [{\n    title: 'Total Sessions',\n    value: '1,280',\n    change: '+15%',\n    trend: 'up'\n  }, {\n    title: 'Avg. Session Duration',\n    value: '3m 45s',\n    change: '+8%',\n    trend: 'up'\n  }, {\n    title: 'Total Interactions',\n    value: '3,790',\n    change: '+12%',\n    trend: 'up'\n  }, {\n    title: 'Avg. Interactions/Session',\n    value: '2.96',\n    change: '+5%',\n    trend: 'up'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-end mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-flex rounded-lg border border-gray-200 p-1\",\n        children: ['7d', '30d', '90d', '1y'].map(range => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setTimeRange(range),\n          className: `px-3 py-1 text-sm font-medium rounded-md ${timeRange === range ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:text-gray-900'}`,\n          children: range\n        }, range, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n      children: metrics.map(metric => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm font-medium text-gray-600\",\n            children: metric.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-2xl font-semibold text-gray-900 mt-1\",\n            children: metric.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-sm font-medium ${metric.trend === 'up' ? 'text-green-600' : 'text-red-600'}`,\n            children: metric.change\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600 ml-2\",\n            children: \"from last period\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this)]\n      }, metric.title, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Session Trends\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-80\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: engagementData,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"sessions\",\n                stroke: \"#2D8C88\",\n                strokeWidth: 2,\n                dot: {\n                  fill: '#2D8C88'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-sm p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Interaction Types\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-80\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: interactionTypes,\n                cx: \"50%\",\n                cy: \"50%\",\n                labelLine: false,\n                outerRadius: 80,\n                fill: \"#8884d8\",\n                dataKey: \"value\",\n                label: ({\n                  name,\n                  percent\n                }) => `${name} ${(percent * 100).toFixed(0)}%`,\n                children: interactionTypes.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: COLORS[index % COLORS.length]\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4 border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: \"Detailed Metrics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Sessions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Avg. Duration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Interactions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: engagementData.map(day => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                children: day.date\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: day.sessions\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: [day.avgDuration, \"s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: day.interactions\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this)]\n            }, day.date, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_s(UserEngagement, \"S/Xk/B2yitnwtU8ogBi58+n1JKE=\");\n_c = UserEngagement;\nexport default UserEngagement;\nvar _c;\n$RefreshReg$(_c, \"UserEngagement\");", "map": {"version": 3, "names": ["React", "useState", "Line<PERSON>hart", "Line", "<PERSON><PERSON><PERSON>", "Bar", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "jsxDEV", "_jsxDEV", "UserEngagement", "_s", "timeRange", "setTimeRange", "engagementData", "date", "sessions", "avgDuration", "interactions", "interactionTypes", "name", "value", "COLORS", "metrics", "title", "change", "trend", "className", "children", "map", "range", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "metric", "width", "height", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "type", "stroke", "strokeWidth", "dot", "fill", "cx", "cy", "labelLine", "outerRadius", "label", "percent", "toFixed", "entry", "index", "length", "day", "_c", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/pages/client/analytics/UserEngagement.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  Line,\n  <PERSON><PERSON>hart,\n  <PERSON>,\n  <PERSON><PERSON>hart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  <PERSON><PERSON><PERSON>,\n  Legend,\n  ResponsiveContainer\n} from 'recharts';\n\nconst UserEngagement = () => {\n  const [timeRange, setTimeRange] = useState('7d');\n\n  // Sample data\n  const engagementData = [\n    { date: '2024-03-01', sessions: 120, avgDuration: 180, interactions: 450 },\n    { date: '2024-03-02', sessions: 150, avgDuration: 185, interactions: 480 },\n    { date: '2024-03-03', sessions: 180, avgDuration: 190, interactions: 520 },\n    { date: '2024-03-04', sessions: 160, avgDuration: 175, interactions: 460 },\n    { date: '2024-03-05', sessions: 200, avgDuration: 195, interactions: 580 },\n    { date: '2024-03-06', sessions: 220, avgDuration: 200, interactions: 620 },\n    { date: '2024-03-07', sessions: 250, avgDuration: 205, interactions: 680 },\n  ];\n\n  const interactionTypes = [\n    { name: 'Product Rotation', value: 35 },\n    { name: 'Color Change', value: 25 },\n    { name: 'Size Adjustment', value: 20 },\n    { name: 'Share', value: 15 },\n    { name: 'Other', value: 5 },\n  ];\n\n  const COLORS = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'];\n\n  const metrics = [\n    {\n      title: 'Total Sessions',\n      value: '1,280',\n      change: '+15%',\n      trend: 'up',\n    },\n    {\n      title: 'Avg. Session Duration',\n      value: '3m 45s',\n      change: '+8%',\n      trend: 'up',\n    },\n    {\n      title: 'Total Interactions',\n      value: '3,790',\n      change: '+12%',\n      trend: 'up',\n    },\n    {\n      title: 'Avg. Interactions/Session',\n      value: '2.96',\n      change: '+5%',\n      trend: 'up',\n    },\n  ];\n\n  return (\n    <div className=\"p-6\">\n      {/* Time Range Selector */}\n      <div className=\"flex justify-end mb-6\">\n        <div className=\"inline-flex rounded-lg border border-gray-200 p-1\">\n          {['7d', '30d', '90d', '1y'].map((range) => (\n            <button\n              key={range}\n              onClick={() => setTimeRange(range)}\n              className={`px-3 py-1 text-sm font-medium rounded-md ${\n                timeRange === range\n                  ? 'bg-[#2D8C88] text-white'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              {range}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n        {metrics.map((metric) => (\n          <div key={metric.title} className=\"bg-white rounded-xl shadow-sm p-6\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">{metric.title}</p>\n              <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{metric.value}</p>\n            </div>\n            <div className=\"mt-4\">\n              <span className={`text-sm font-medium ${\n                metric.trend === 'up' ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {metric.change}\n              </span>\n              <span className=\"text-sm text-gray-600 ml-2\">from last period</span>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Engagement Trends */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\n        <div className=\"bg-white rounded-xl shadow-sm p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Session Trends</h3>\n          <div className=\"h-80\">\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <LineChart data={engagementData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"date\" />\n                <YAxis />\n                <Tooltip />\n                <Line\n                  type=\"monotone\"\n                  dataKey=\"sessions\"\n                  stroke=\"#2D8C88\"\n                  strokeWidth={2}\n                  dot={{ fill: '#2D8C88' }}\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Interaction Types</h3>\n          <div className=\"h-80\">\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <PieChart>\n                <Pie\n                  data={interactionTypes}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"value\"\n                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n                >\n                  {interactionTypes.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n                  ))}\n                </Pie>\n                <Tooltip />\n              </PieChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n      </div>\n\n      {/* Detailed Metrics */}\n      <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Detailed Metrics</h3>\n        </div>\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Date</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Sessions</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Avg. Duration</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Interactions</th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {engagementData.map((day) => (\n                <tr key={day.date} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">{day.date}</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{day.sessions}</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{day.avgDuration}s</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{day.interactions}</td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default UserEngagement; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,GAAG,EACHC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,QACd,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElB,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAMoB,cAAc,GAAG,CACrB;IAAEC,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,GAAG;IAAEC,WAAW,EAAE,GAAG;IAAEC,YAAY,EAAE;EAAI,CAAC,EAC1E;IAAEH,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,GAAG;IAAEC,WAAW,EAAE,GAAG;IAAEC,YAAY,EAAE;EAAI,CAAC,EAC1E;IAAEH,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,GAAG;IAAEC,WAAW,EAAE,GAAG;IAAEC,YAAY,EAAE;EAAI,CAAC,EAC1E;IAAEH,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,GAAG;IAAEC,WAAW,EAAE,GAAG;IAAEC,YAAY,EAAE;EAAI,CAAC,EAC1E;IAAEH,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,GAAG;IAAEC,WAAW,EAAE,GAAG;IAAEC,YAAY,EAAE;EAAI,CAAC,EAC1E;IAAEH,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,GAAG;IAAEC,WAAW,EAAE,GAAG;IAAEC,YAAY,EAAE;EAAI,CAAC,EAC1E;IAAEH,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,GAAG;IAAEC,WAAW,EAAE,GAAG;IAAEC,YAAY,EAAE;EAAI,CAAC,CAC3E;EAED,MAAMC,gBAAgB,GAAG,CACvB;IAAEC,IAAI,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAG,CAAC,EACvC;IAAED,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAG,CAAC,EACnC;IAAED,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAG,CAAC,EACtC;IAAED,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAG,CAAC,EAC5B;IAAED,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAE,CAAC,CAC5B;EAED,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAEtE,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,gBAAgB;IACvBH,KAAK,EAAE,OAAO;IACdI,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,uBAAuB;IAC9BH,KAAK,EAAE,QAAQ;IACfI,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,oBAAoB;IAC3BH,KAAK,EAAE,OAAO;IACdI,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,2BAA2B;IAClCH,KAAK,EAAE,MAAM;IACbI,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEjB,OAAA;IAAKkB,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAElBnB,OAAA;MAAKkB,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpCnB,OAAA;QAAKkB,SAAS,EAAC,mDAAmD;QAAAC,QAAA,EAC/D,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAACC,GAAG,CAAEC,KAAK,iBACpCrB,OAAA;UAEEsB,OAAO,EAAEA,CAAA,KAAMlB,YAAY,CAACiB,KAAK,CAAE;UACnCH,SAAS,EAAE,4CACTf,SAAS,KAAKkB,KAAK,GACf,yBAAyB,GACzB,mCAAmC,EACtC;UAAAF,QAAA,EAEFE;QAAK,GARDA,KAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASJ,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAKkB,SAAS,EAAC,2DAA2D;MAAAC,QAAA,EACvEL,OAAO,CAACM,GAAG,CAAEO,MAAM,iBAClB3B,OAAA;QAAwBkB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBACnEnB,OAAA;UAAAmB,QAAA,gBACEnB,OAAA;YAAGkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAEQ,MAAM,CAACZ;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnE1B,OAAA;YAAGkB,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAEQ,MAAM,CAACf;UAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,eACN1B,OAAA;UAAKkB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBnB,OAAA;YAAMkB,SAAS,EAAE,uBACfS,MAAM,CAACV,KAAK,KAAK,IAAI,GAAG,gBAAgB,GAAG,cAAc,EACxD;YAAAE,QAAA,EACAQ,MAAM,CAACX;UAAM;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACP1B,OAAA;YAAMkB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAgB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA,GAZEC,MAAM,CAACZ,KAAK;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAajB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN1B,OAAA;MAAKkB,SAAS,EAAC,4CAA4C;MAAAC,QAAA,gBACzDnB,OAAA;QAAKkB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDnB,OAAA;UAAIkB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1E1B,OAAA;UAAKkB,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBnB,OAAA,CAACF,mBAAmB;YAAC8B,KAAK,EAAC,MAAM;YAACC,MAAM,EAAC,MAAM;YAAAV,QAAA,eAC7CnB,OAAA,CAACd,SAAS;cAAC4C,IAAI,EAAEzB,cAAe;cAAAc,QAAA,gBAC9BnB,OAAA,CAACL,aAAa;gBAACoC,eAAe,EAAC;cAAK;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC1B,OAAA,CAACP,KAAK;gBAACuC,OAAO,EAAC;cAAM;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxB1B,OAAA,CAACN,KAAK;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT1B,OAAA,CAACJ,OAAO;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX1B,OAAA,CAACb,IAAI;gBACH8C,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,UAAU;gBAClBE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;kBAAEC,IAAI,EAAE;gBAAU;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1B,OAAA;QAAKkB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDnB,OAAA;UAAIkB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E1B,OAAA;UAAKkB,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBnB,OAAA,CAACF,mBAAmB;YAAC8B,KAAK,EAAC,MAAM;YAACC,MAAM,EAAC,MAAM;YAAAV,QAAA,eAC7CnB,OAAA,CAACV,QAAQ;cAAA6B,QAAA,gBACPnB,OAAA,CAACT,GAAG;gBACFuC,IAAI,EAAEpB,gBAAiB;gBACvB4B,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACRC,SAAS,EAAE,KAAM;gBACjBC,WAAW,EAAE,EAAG;gBAChBJ,IAAI,EAAC,SAAS;gBACdL,OAAO,EAAC,OAAO;gBACfU,KAAK,EAAEA,CAAC;kBAAE/B,IAAI;kBAAEgC;gBAAQ,CAAC,KAAK,GAAGhC,IAAI,IAAI,CAACgC,OAAO,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAI;gBAAAzB,QAAA,EAEtET,gBAAgB,CAACU,GAAG,CAAC,CAACyB,KAAK,EAAEC,KAAK,kBACjC9C,OAAA,CAACR,IAAI;kBAAuB6C,IAAI,EAAExB,MAAM,CAACiC,KAAK,GAAGjC,MAAM,CAACkC,MAAM;gBAAE,GAArD,QAAQD,KAAK,EAAE;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAwC,CACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1B,OAAA,CAACJ,OAAO;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAKkB,SAAS,EAAC,+CAA+C;MAAAC,QAAA,gBAC5DnB,OAAA;QAAKkB,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eACjDnB,OAAA;UAAIkB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAgB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eACN1B,OAAA;QAAKkB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BnB,OAAA;UAAOkB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpDnB,OAAA;YAAOkB,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC3BnB,OAAA;cAAAmB,QAAA,gBACEnB,OAAA;gBAAIkB,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxG1B,OAAA;gBAAIkB,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5G1B,OAAA;gBAAIkB,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjH1B,OAAA;gBAAIkB,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR1B,OAAA;YAAOkB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EACjDd,cAAc,CAACe,GAAG,CAAE4B,GAAG,iBACtBhD,OAAA;cAAmBkB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC7CnB,OAAA;gBAAIkB,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAAE6B,GAAG,CAAC1C;cAAI;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7F1B,OAAA;gBAAIkB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAE6B,GAAG,CAACzC;cAAQ;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrF1B,OAAA;gBAAIkB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,GAAE6B,GAAG,CAACxC,WAAW,EAAC,GAAC;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzF1B,OAAA;gBAAIkB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAE6B,GAAG,CAACvC;cAAY;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,GAJlFsB,GAAG,CAAC1C,IAAI;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKb,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxB,EAAA,CA3KID,cAAc;AAAAgD,EAAA,GAAdhD,cAAc;AA6KpB,eAAeA,cAAc;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}