import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import Home from './pages/Home';
import Login from './pages/Login';
import Watches from './pages/Watches';
import Bracelets from './pages/Bracelets';
import HowItWorks from './pages/HowItWorks';
import WhyViaTryon from './pages/WhyViaTryon';
import Contact from './pages/Contact';
import SearchResults from './pages/SearchResults';
import DemoForm from './components/DemoForm';
import VirtualTryOn from './pages/VirtualTryOn';
import ProductDetails from './pages/ProductDetails';
import Requirements from './pages/Requirements';
import AdminDashboard from './pages/admin/AdminDashboard';
import ClientDashboard from './pages/client/ClientDashboard';
import Clients from './pages/admin/Clients';
import TryOnAnalytics from './pages/admin/TryOnAnalytics';
import Settings from './pages/admin/Settings';
import ClientAnalytics from './pages/client/analytics/ClientAnalytics';

// Protected Route component
const ProtectedRoute = ({ children, allowedRoles }) => {
  const user = JSON.parse(localStorage.getItem('user'));
  
  if (!user) {
    return <Navigate to="/login" />;
  }

  if (allowedRoles && !allowedRoles.includes(user.role)) {
    return <Navigate to="/" />;
  }

  return children;
};

function App() {
  const user = JSON.parse(localStorage.getItem('user'));
  const isAdminRoute = window.location.pathname.startsWith('/admin');
  const isClientRoute = window.location.pathname.startsWith('/client');
  const showNavbarFooter = !isAdminRoute && !isClientRoute;

  return (
    <Router>
      <div className="min-h-screen flex flex-col">
        {showNavbarFooter && <Navbar />}
        <main className="flex-grow">
          <Routes>
            {/* Public Routes */}
            <Route path="/" element={<Home />} />
            <Route path="/login" element={<Login />} />
            <Route path="/watches" element={<Watches />} />
            <Route path="/bracelets" element={<Bracelets />} />
            <Route path="/how-it-works" element={<HowItWorks />} />
            <Route path="/why-viatryon" element={<WhyViaTryon />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/schedule-demo" element={<DemoForm />} />
            <Route path="/search" element={<SearchResults />} />
            <Route path="/virtual-try-on" element={<VirtualTryOn />} />
            <Route path="/requirements" element={<Requirements />} />
            <Route path="/:category/:id" element={<ProductDetails />} />

            {/* Admin Routes */}
            <Route
              path="/admin"
              element={
                <ProtectedRoute allowedRoles={['admin']}>
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/clients"
              element={
                <ProtectedRoute allowedRoles={['admin']}>
                  <Clients />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/tryon-analytics"
              element={
                <ProtectedRoute allowedRoles={['admin']}>
                  <TryOnAnalytics />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/settings"
              element={
                <ProtectedRoute allowedRoles={['admin']}>
                  <Settings />
                </ProtectedRoute>
              }
            />

            {/* Client Routes */}
            <Route
              path="/client/dashboard"
              element={
                <ProtectedRoute allowedRoles={['client']}>
                  <ClientDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/client/analytics/*"
              element={
                <ProtectedRoute allowedRoles={['client']}>
                  <ClientAnalytics />
                </ProtectedRoute>
              }
            />
            <Route
              path="/virtual-try-on"
              element={
                <ProtectedRoute allowedRoles={['client']}>
                  <VirtualTryOn />
                </ProtectedRoute>
              }
            />
          </Routes>
        </main>
        {showNavbarFooter && <Footer />}
      </div>
    </Router>
  );
}

export default App;


