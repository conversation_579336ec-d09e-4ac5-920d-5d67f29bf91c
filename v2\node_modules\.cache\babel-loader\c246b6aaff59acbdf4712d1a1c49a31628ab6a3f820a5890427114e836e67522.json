{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\components\\\\client\\\\ClientSidebar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ClientSidebar = ({\n  isOpen,\n  onClose,\n  collapsed,\n  setCollapsed\n}) => {\n  _s();\n  const location = useLocation();\n  const user = JSON.parse(localStorage.getItem('user')) || {};\n  const menuItems = [{\n    title: 'Dashboard',\n    path: '/client/dashboard',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Virtual Try-On',\n    path: '/virtual-try-on',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Analytics',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"h-6 w-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 9\n    }, this),\n    subItems: [{\n      title: 'Overview',\n      path: '/client/analytics/overview'\n    }, {\n      title: 'Product Performance',\n      path: '/client/analytics/product-performance'\n    }, {\n      title: 'User Engagement',\n      path: '/client/analytics/user-engagement'\n    }, {\n      title: 'Conversion Rates',\n      path: '/client/analytics/conversion-rates'\n    }, {\n      title: 'Time Analysis',\n      path: '/client/analytics/time-analysis'\n    }, {\n      title: 'Device Stats',\n      path: '/client/analytics/device-stats'\n    }, {\n      title: 'Geographic Data',\n      path: '/client/analytics/geographic-data'\n    }]\n  }];\n\n  // Sidebar for mobile (animated)\n  const mobileSidebar = /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: isOpen && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        exit: {\n          opacity: 0\n        },\n        className: \"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\",\n        onClick: onClose\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          x: -280\n        },\n        animate: {\n          x: 0\n        },\n        exit: {\n          x: -280\n        },\n        transition: {\n          duration: 0.3,\n          ease: 'easeInOut'\n        },\n        className: \"fixed left-0 top-0 h-screen bg-white border-r border-gray-200 z-50 w-[280px] md:hidden\",\n        children: renderSidebarContent()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n\n  // Sidebar for desktop (always visible)\n  const desktopSidebar = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"hidden md:block fixed left-0 top-0 h-screen bg-white border-r border-gray-200 z-50\",\n    style: {\n      width: collapsed ? 80 : 280\n    },\n    children: renderSidebarContent()\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n  function renderSidebarContent() {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col h-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"flex items-center justify-center w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/imgs/logo-only.png\",\n            alt: \"ViaTryon\",\n            className: collapsed ? 'h-8 w-8 transition-all duration-200' : 'h-8 w-auto transition-all duration-200',\n            style: collapsed ? {\n              minWidth: 32\n            } : {\n              minWidth: 32,\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-serif text-xl font-medium text-[#1F2937] ml-2\",\n            children: \"ViaTryon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCollapsed(!collapsed),\n          className: \"p-2 rounded-lg hover:bg-gray-100 text-gray-600 hidden md:block\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-5 w-5\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: collapsed ? \"M13 5l7 7-7 7M5 5l7 7-7 7\" : \"M11 19l-7-7 7-7m8 14l-7-7 7-7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"flex-1 p-4 space-y-1 overflow-y-auto\",\n        children: menuItems.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n          children: item.subItems ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center space-x-3 px-4 py-3 rounded-lg ${location.pathname.startsWith('/client/analytics') ? 'bg-[#2D8C88]/10 text-[#2D8C88]' : 'text-gray-600'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex-shrink-0\",\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 21\n              }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 36\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 19\n            }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-8 space-y-1\",\n              children: item.subItems.map(subItem => /*#__PURE__*/_jsxDEV(Link, {\n                to: subItem.path,\n                className: `block px-4 py-2 rounded-lg text-sm ${location.pathname === subItem.path ? 'bg-[#2D8C88]/10 text-[#2D8C88]' : 'text-gray-600 hover:bg-gray-100'}`,\n                children: subItem.title\n              }, subItem.path, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Link, {\n            to: item.path,\n            className: `flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-200 ${location.pathname === item.path ? 'bg-[#2D8C88]/10 text-[#2D8C88]' : 'text-gray-600 hover:bg-gray-100'}`,\n            onClick: () => {\n              if (window.innerWidth < 768) {\n                onClose();\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex-shrink-0\",\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 19\n            }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 34\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 17\n          }, this)\n        }, item.path || item.title, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-t border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88]\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-900\",\n              children: \"Client User\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [mobileSidebar, desktopSidebar]\n  }, void 0, true);\n};\n_s(ClientSidebar, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = ClientSidebar;\nexport default ClientSidebar;\nvar _c;\n$RefreshReg$(_c, \"ClientSidebar\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "motion", "AnimatePresence", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ClientSidebar", "isOpen", "onClose", "collapsed", "setCollapsed", "_s", "location", "user", "JSON", "parse", "localStorage", "getItem", "menuItems", "title", "path", "icon", "xmlns", "className", "fill", "viewBox", "stroke", "children", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "subItems", "mobileSidebar", "div", "initial", "opacity", "animate", "exit", "onClick", "x", "transition", "duration", "ease", "renderSidebarContent", "desktopSidebar", "style", "width", "to", "src", "alt", "min<PERSON><PERSON><PERSON>", "marginRight", "map", "item", "pathname", "startsWith", "subItem", "window", "innerWidth", "email", "_c", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/components/client/ClientSidebar.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst ClientSidebar = ({ isOpen, onClose, collapsed, setCollapsed }) => {\n  const location = useLocation();\n  const user = JSON.parse(localStorage.getItem('user')) || {};\n\n  const menuItems = [\n    {\n      title: 'Dashboard',\n      path: '/client/dashboard',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\" />\n        </svg>\n      ),\n    },\n    {\n      title: 'Virtual Try-On',\n      path: '/virtual-try-on',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n        </svg>\n      ),\n    },\n    {\n      title: 'Analytics',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      ),\n      subItems: [\n        {\n          title: 'Overview',\n          path: '/client/analytics/overview',\n        },\n        {\n          title: 'Product Performance',\n          path: '/client/analytics/product-performance',\n        },\n        {\n          title: 'User Engagement',\n          path: '/client/analytics/user-engagement',\n        },\n        {\n          title: 'Conversion Rates',\n          path: '/client/analytics/conversion-rates',\n        },\n        {\n          title: 'Time Analysis',\n          path: '/client/analytics/time-analysis',\n        },\n        {\n          title: 'Device Stats',\n          path: '/client/analytics/device-stats',\n        },\n        {\n          title: 'Geographic Data',\n          path: '/client/analytics/geographic-data',\n        },\n      ],\n    },\n  ];\n\n  // Sidebar for mobile (animated)\n  const mobileSidebar = (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\n            onClick={onClose}\n          />\n          <motion.div\n            initial={{ x: -280 }}\n            animate={{ x: 0 }}\n            exit={{ x: -280 }}\n            transition={{ duration: 0.3, ease: 'easeInOut' }}\n            className=\"fixed left-0 top-0 h-screen bg-white border-r border-gray-200 z-50 w-[280px] md:hidden\"\n          >\n            {renderSidebarContent()}\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n\n  // Sidebar for desktop (always visible)\n  const desktopSidebar = (\n    <div className=\"hidden md:block fixed left-0 top-0 h-screen bg-white border-r border-gray-200 z-50\" style={{ width: collapsed ? 80 : 280 }}>\n      {renderSidebarContent()}\n    </div>\n  );\n\n  function renderSidebarContent() {\n    return (\n      <div className=\"flex flex-col h-full\">\n        {/* Logo */}\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n          <Link to=\"/\" className=\"flex items-center justify-center w-full\">\n            <img\n              src=\"/imgs/logo-only.png\"\n              alt=\"ViaTryon\"\n              className={collapsed ? 'h-8 w-8 transition-all duration-200' : 'h-8 w-auto transition-all duration-200'}\n              style={collapsed ? { minWidth: 32 } : { minWidth: 32, marginRight: 8 }}\n            />\n            {!collapsed && (\n              <span className=\"font-serif text-xl font-medium text-[#1F2937] ml-2\">ViaTryon</span>\n            )}\n          </Link>\n          <button\n            onClick={() => setCollapsed(!collapsed)}\n            className=\"p-2 rounded-lg hover:bg-gray-100 text-gray-600 hidden md:block\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              className=\"h-5 w-5\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d={collapsed ? \"M13 5l7 7-7 7M5 5l7 7-7 7\" : \"M11 19l-7-7 7-7m8 14l-7-7 7-7\"}\n              />\n            </svg>\n          </button>\n        </div>\n        {/* Menu Items */}\n        <nav className=\"flex-1 p-4 space-y-1 overflow-y-auto\">\n          {menuItems.map((item) => (\n            <div key={item.path || item.title}>\n              {item.subItems ? (\n                <div className=\"space-y-1\">\n                  <div className={`flex items-center space-x-3 px-4 py-3 rounded-lg ${\n                    location.pathname.startsWith('/client/analytics')\n                      ? 'bg-[#2D8C88]/10 text-[#2D8C88]'\n                      : 'text-gray-600'\n                  }`}>\n                    <span className=\"flex-shrink-0\">{item.icon}</span>\n                    {!collapsed && <span className=\"font-medium\">{item.title}</span>}\n                  </div>\n                  {!collapsed && (\n                    <div className=\"ml-8 space-y-1\">\n                      {item.subItems.map((subItem) => (\n                        <Link\n                          key={subItem.path}\n                          to={subItem.path}\n                          className={`block px-4 py-2 rounded-lg text-sm ${\n                            location.pathname === subItem.path\n                              ? 'bg-[#2D8C88]/10 text-[#2D8C88]'\n                              : 'text-gray-600 hover:bg-gray-100'\n                          }`}\n                        >\n                          {subItem.title}\n                        </Link>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              ) : (\n                <Link\n                  to={item.path}\n                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-200 ${\n                    location.pathname === item.path\n                      ? 'bg-[#2D8C88]/10 text-[#2D8C88]'\n                      : 'text-gray-600 hover:bg-gray-100'\n                  }`}\n                  onClick={() => {\n                    if (window.innerWidth < 768) {\n                      onClose();\n                    }\n                  }}\n                >\n                  <span className=\"flex-shrink-0\">{item.icon}</span>\n                  {!collapsed && <span className=\"font-medium\">{item.title}</span>}\n                </Link>\n              )}\n            </div>\n          ))}\n        </nav>\n        {/* User Profile */}\n        <div className=\"p-4 border-t border-gray-200\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center text-[#2D8C88]\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n              </svg>\n            </div>\n            {!collapsed && (\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">Client User</p>\n                <p className=\"text-xs text-gray-500\">{user.email}</p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      {mobileSidebar}\n      {desktopSidebar}\n    </>\n  );\n};\n\nexport default ClientSidebar; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExD,MAAMC,aAAa,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,SAAS;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;EAE3D,MAAMC,SAAS,GAAG,CAChB;IACEC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,eACFlB,OAAA;MAAKmB,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/GxB,OAAA;QAAMyB,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAsQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3U;EAET,CAAC,EACD;IACEhB,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,eACFlB,OAAA;MAAKmB,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/GxB,OAAA;QAAMyB,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAA2G;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChL;EAET,CAAC,EACD;IACEhB,KAAK,EAAE,WAAW;IAClBE,IAAI,eACFlB,OAAA;MAAKmB,KAAK,EAAC,4BAA4B;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC/GxB,OAAA;QAAMyB,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAsM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3Q,CACN;IACDC,QAAQ,EAAE,CACR;MACEjB,KAAK,EAAE,UAAU;MACjBC,IAAI,EAAE;IACR,CAAC,EACD;MACED,KAAK,EAAE,qBAAqB;MAC5BC,IAAI,EAAE;IACR,CAAC,EACD;MACED,KAAK,EAAE,iBAAiB;MACxBC,IAAI,EAAE;IACR,CAAC,EACD;MACED,KAAK,EAAE,kBAAkB;MACzBC,IAAI,EAAE;IACR,CAAC,EACD;MACED,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE;IACR,CAAC,EACD;MACED,KAAK,EAAE,cAAc;MACrBC,IAAI,EAAE;IACR,CAAC,EACD;MACED,KAAK,EAAE,iBAAiB;MACxBC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC,CACF;;EAED;EACA,MAAMiB,aAAa,gBACjBlC,OAAA,CAACF,eAAe;IAAA0B,QAAA,EACbpB,MAAM,iBACLJ,OAAA,CAAAE,SAAA;MAAAsB,QAAA,gBACExB,OAAA,CAACH,MAAM,CAACsC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBE,IAAI,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACrBjB,SAAS,EAAC,qDAAqD;QAC/DoB,OAAO,EAAEnC;MAAQ;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eACFhC,OAAA,CAACH,MAAM,CAACsC,GAAG;QACTC,OAAO,EAAE;UAAEK,CAAC,EAAE,CAAC;QAAI,CAAE;QACrBH,OAAO,EAAE;UAAEG,CAAC,EAAE;QAAE,CAAE;QAClBF,IAAI,EAAE;UAAEE,CAAC,EAAE,CAAC;QAAI,CAAE;QAClBC,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,IAAI,EAAE;QAAY,CAAE;QACjDxB,SAAS,EAAC,wFAAwF;QAAAI,QAAA,EAEjGqB,oBAAoB,CAAC;MAAC;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA,eACb;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAClB;;EAED;EACA,MAAMc,cAAc,gBAClB9C,OAAA;IAAKoB,SAAS,EAAC,oFAAoF;IAAC2B,KAAK,EAAE;MAAEC,KAAK,EAAE1C,SAAS,GAAG,EAAE,GAAG;IAAI,CAAE;IAAAkB,QAAA,EACxIqB,oBAAoB,CAAC;EAAC;IAAAhB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpB,CACN;EAED,SAASa,oBAAoBA,CAAA,EAAG;IAC9B,oBACE7C,OAAA;MAAKoB,SAAS,EAAC,sBAAsB;MAAAI,QAAA,gBAEnCxB,OAAA;QAAKoB,SAAS,EAAC,gEAAgE;QAAAI,QAAA,gBAC7ExB,OAAA,CAACL,IAAI;UAACsD,EAAE,EAAC,GAAG;UAAC7B,SAAS,EAAC,yCAAyC;UAAAI,QAAA,gBAC9DxB,OAAA;YACEkD,GAAG,EAAC,qBAAqB;YACzBC,GAAG,EAAC,UAAU;YACd/B,SAAS,EAAEd,SAAS,GAAG,qCAAqC,GAAG,wCAAyC;YACxGyC,KAAK,EAAEzC,SAAS,GAAG;cAAE8C,QAAQ,EAAE;YAAG,CAAC,GAAG;cAAEA,QAAQ,EAAE,EAAE;cAAEC,WAAW,EAAE;YAAE;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,EACD,CAAC1B,SAAS,iBACTN,OAAA;YAAMoB,SAAS,EAAC,oDAAoD;YAAAI,QAAA,EAAC;UAAQ;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACpF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACPhC,OAAA;UACEwC,OAAO,EAAEA,CAAA,KAAMjC,YAAY,CAAC,CAACD,SAAS,CAAE;UACxCc,SAAS,EAAC,gEAAgE;UAAAI,QAAA,eAE1ExB,OAAA;YACEmB,KAAK,EAAC,4BAA4B;YAClCC,SAAS,EAAC,SAAS;YACnBC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnBC,MAAM,EAAC,cAAc;YAAAC,QAAA,eAErBxB,OAAA;cACEyB,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAEtB,SAAS,GAAG,2BAA2B,GAAG;YAAgC;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENhC,OAAA;QAAKoB,SAAS,EAAC,sCAAsC;QAAAI,QAAA,EAClDT,SAAS,CAACuC,GAAG,CAAEC,IAAI,iBAClBvD,OAAA;UAAAwB,QAAA,EACG+B,IAAI,CAACtB,QAAQ,gBACZjC,OAAA;YAAKoB,SAAS,EAAC,WAAW;YAAAI,QAAA,gBACxBxB,OAAA;cAAKoB,SAAS,EAAE,oDACdX,QAAQ,CAAC+C,QAAQ,CAACC,UAAU,CAAC,mBAAmB,CAAC,GAC7C,gCAAgC,GAChC,eAAe,EAClB;cAAAjC,QAAA,gBACDxB,OAAA;gBAAMoB,SAAS,EAAC,eAAe;gBAAAI,QAAA,EAAE+B,IAAI,CAACrC;cAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACjD,CAAC1B,SAAS,iBAAIN,OAAA;gBAAMoB,SAAS,EAAC,aAAa;gBAAAI,QAAA,EAAE+B,IAAI,CAACvC;cAAK;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,EACL,CAAC1B,SAAS,iBACTN,OAAA;cAAKoB,SAAS,EAAC,gBAAgB;cAAAI,QAAA,EAC5B+B,IAAI,CAACtB,QAAQ,CAACqB,GAAG,CAAEI,OAAO,iBACzB1D,OAAA,CAACL,IAAI;gBAEHsD,EAAE,EAAES,OAAO,CAACzC,IAAK;gBACjBG,SAAS,EAAE,sCACTX,QAAQ,CAAC+C,QAAQ,KAAKE,OAAO,CAACzC,IAAI,GAC9B,gCAAgC,GAChC,iCAAiC,EACpC;gBAAAO,QAAA,EAEFkC,OAAO,CAAC1C;cAAK,GART0C,OAAO,CAACzC,IAAI;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASb,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAENhC,OAAA,CAACL,IAAI;YACHsD,EAAE,EAAEM,IAAI,CAACtC,IAAK;YACdG,SAAS,EAAE,mFACTX,QAAQ,CAAC+C,QAAQ,KAAKD,IAAI,CAACtC,IAAI,GAC3B,gCAAgC,GAChC,iCAAiC,EACpC;YACHuB,OAAO,EAAEA,CAAA,KAAM;cACb,IAAImB,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE;gBAC3BvD,OAAO,CAAC,CAAC;cACX;YACF,CAAE;YAAAmB,QAAA,gBAEFxB,OAAA;cAAMoB,SAAS,EAAC,eAAe;cAAAI,QAAA,EAAE+B,IAAI,CAACrC;YAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACjD,CAAC1B,SAAS,iBAAIN,OAAA;cAAMoB,SAAS,EAAC,aAAa;cAAAI,QAAA,EAAE+B,IAAI,CAACvC;YAAK;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D;QACP,GA9COuB,IAAI,CAACtC,IAAI,IAAIsC,IAAI,CAACvC,KAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+C5B,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENhC,OAAA;QAAKoB,SAAS,EAAC,8BAA8B;QAAAI,QAAA,eAC3CxB,OAAA;UAAKoB,SAAS,EAAC,6BAA6B;UAAAI,QAAA,gBAC1CxB,OAAA;YAAKoB,SAAS,EAAC,wFAAwF;YAAAI,QAAA,eACrGxB,OAAA;cAAKmB,KAAK,EAAC,4BAA4B;cAACC,SAAS,EAAC,SAAS;cAACC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAC,QAAA,eAC/GxB,OAAA;gBAAMyB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAqE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1I;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACL,CAAC1B,SAAS,iBACTN,OAAA;YAAAwB,QAAA,gBACExB,OAAA;cAAGoB,SAAS,EAAC,mCAAmC;cAAAI,QAAA,EAAC;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChEhC,OAAA;cAAGoB,SAAS,EAAC,uBAAuB;cAAAI,QAAA,EAAEd,IAAI,CAACmD;YAAK;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhC,OAAA,CAAAE,SAAA;IAAAsB,QAAA,GACGU,aAAa,EACbY,cAAc;EAAA,eACf,CAAC;AAEP,CAAC;AAACtC,EAAA,CAnNIL,aAAa;EAAA,QACAP,WAAW;AAAA;AAAAkE,EAAA,GADxB3D,aAAa;AAqNnB,eAAeA,aAAa;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}